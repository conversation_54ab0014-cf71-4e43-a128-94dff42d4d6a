import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = 3001;

// In-memory storage for development with some test translations
let storedTranslations = [
  // Dutch translations for test keys
  {
    id: 'test-nl-1',
    keyId: '1',
    language: 'nl',
    content: 'Uw AI Juridische Assistent voor Moderne Rechtspraktijk',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-nl-2',
    keyId: '2',
    language: 'nl',
    content: 'AI-aangedreven juridisch onderzoek, concepten en praktijkbeheertools ontworpen voor solo-praktijken en kleine advocatenkantoren.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-nl-3',
    keyId: '3',
    language: 'nl',
    content: 'Gecertificeerd door de Belgische Orde van Advocaten voor juridische technologie compliance.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-nl-5',
    keyId: '5',
    language: 'nl',
    content: 'Welkom {{name}} bij {{company}}! We zijn enthousiast om uw praktijk te helpen groeien.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  // French translations for test keys
  {
    id: 'test-fr-1',
    keyId: '1',
    language: 'fr',
    content: 'Votre Assistant Juridique IA pour la Pratique Juridique Moderne',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-fr-2',
    keyId: '2',
    language: 'fr',
    content: 'Outils de recherche juridique, de rédaction et de gestion de cabinet alimentés par l\'IA, conçus pour les praticiens indépendants et les petits cabinets d\'avocats.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-fr-3',
    keyId: '3',
    language: 'fr',
    content: 'Certifié par l\'Ordre des Avocats Belges pour la conformité technologique juridique.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'test-fr-5',
    keyId: '5',
    language: 'fr',
    content: 'Bienvenue {{name}} chez {{company}} ! Nous sommes ravis d\'aider votre cabinet à se développer.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Middleware
app.use(cors({
  origin: 'http://localhost:5174',
  credentials: true
}));
app.use(express.json());

// Mock admin authentication for development
app.all('/api/admin/auth', (req, res) => {
  const action = req.query.action;

  console.log(`📡 API Request: ${req.method} /api/admin/auth?action=${action}`);

  if (action === 'login') {
    const { email, password } = req.body;

    // Mock authentication - check for the test credentials
    if (email === '<EMAIL>' && password === 'admin123') {
      // Set a mock cookie
      res.setHeader('Set-Cookie', [
        'admin_token=mock-dev-token; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict'
      ]);

      return res.status(200).json({
        success: true,
        user: {
          id: 'mock-admin-id',
          email: '<EMAIL>',
          name: 'Alice Van Mierlo'
        }
      });
    } else {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
  }

  if (action === 'verify') {
    // Mock verification - always return success for development
    return res.status(200).json({
      success: true,
      user: {
        id: 'mock-admin-id',
        email: '<EMAIL>',
        name: 'Alice Van Mierlo'
      }
    });
  }

  if (action === 'logout') {
    res.setHeader('Set-Cookie', [
      'admin_token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict'
    ]);
    return res.status(200).json({ success: true });
  }

  return res.status(400).json({ error: 'Invalid action' });
});

// Mock translation keys for development
app.get('/api/translations/keys', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/keys`, req.query);

  const { country, contentType, category } = req.query;

  // Enhanced translation keys with visibility rules for testing
  let mockKeys = [
    // Shared content - shows in both US and Belgium
    {
      id: '1',
      key: 'hero.title',
      category: 'hero',
      description: 'Main hero title',
      defaultContent: 'Your AI Legal Assistant for Modern Law Practice',
      showInCountries: ['US', 'BE'],
      hideInCountries: [],
      contentType: 'shared',
      metadata: {
        styling: {
          belgiumClassName: 'text-blue-800 font-bold',
          usClassName: 'text-gray-900 font-bold'
        }
      }
    },
    {
      id: '2',
      key: 'hero.subtitle',
      category: 'hero',
      description: 'Main hero subtitle',
      defaultContent: 'AI-powered legal research, drafting, and practice management tools designed for solo practitioners and small law firms.',
      showInCountries: ['US', 'BE'],
      hideInCountries: [],
      contentType: 'shared'
    },

    // Belgium-only content
    {
      id: '3',
      key: 'legal.belgian_certification',
      category: 'legal',
      description: 'Belgian bar certification notice',
      defaultContent: 'Certified by the Belgian Bar Association for legal technology compliance.',
      showInCountries: ['BE'],
      hideInCountries: [],
      contentType: 'belgium-only'
    },

    // US-only content
    {
      id: '4',
      key: 'legal.us_bar_admission',
      category: 'legal',
      description: 'US bar admission notice',
      defaultContent: 'Licensed to practice in Texas, Florida, and New York.',
      showInCountries: ['US'],
      hideInCountries: [],
      contentType: 'us-only'
    },

    // Test content with variables
    {
      id: '5',
      key: 'welcome.message',
      category: 'general',
      description: 'Welcome message with variables',
      defaultContent: 'Welcome {{name}} to {{company}}! We\'re excited to help your practice grow.',
      showInCountries: ['US', 'BE'],
      hideInCountries: [],
      contentType: 'shared'
    },
    { id: '3', key: 'hero.tagline', category: 'hero', description: 'Hero tagline emphasizing capability', defaultContent: 'AiLex can do it ALL for you.' },
    { id: '4', key: 'hero.cta_primary', category: 'hero', description: 'Primary call-to-action button', defaultContent: 'Get Started Free' },
    { id: '5', key: 'hero.cta_secondary', category: 'hero', description: 'Secondary call-to-action button', defaultContent: 'Watch Demo' },

    // Problem Solution Section
    { id: '6', key: 'problems.admin_time.title', category: 'problems', description: 'Admin time problem title', defaultContent: '40% lawyer time is unbilled admin.' },
    { id: '7', key: 'problems.admin_time.description', category: 'problems', description: 'Admin time problem description', defaultContent: 'The average solo attorney loses nearly half their potential billable hours to administrative tasks.' },
    { id: '8', key: 'problems.missed_calls.title', category: 'problems', description: 'Missed calls problem title', defaultContent: 'Missed intake calls cost solos $7k+/yr.' },
    { id: '9', key: 'problems.missed_calls.description', category: 'problems', description: 'Missed calls problem description', defaultContent: 'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.' },
    { id: '10', key: 'problems.expensive_software.title', category: 'problems', description: 'Expensive software problem title', defaultContent: 'Big-law software starts at $199/user.' },
    { id: '11', key: 'problems.expensive_software.description', category: 'problems', description: 'Expensive software problem description', defaultContent: 'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.' },

    // Solutions
    { id: '12', key: 'solutions.receptionist.title', category: 'solutions', description: 'AI Receptionist solution title', defaultContent: 'Never Miss a Lead — AiLex Answers Every Call' },
    { id: '13', key: 'solutions.receptionist.description', category: 'solutions', description: 'AI Receptionist solution description', defaultContent: 'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.' },
    { id: '14', key: 'solutions.case_management.title', category: 'solutions', description: 'Case management solution title', defaultContent: 'Never Miss a Deadline — AiLex Keeps Every Case on Track' },
    { id: '15', key: 'solutions.case_management.description', category: 'solutions', description: 'Case management solution description', defaultContent: 'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.' },
    { id: '16', key: 'solutions.document_drafting.title', category: 'solutions', description: 'Document drafting solution title', defaultContent: 'Never Redraft a Motion — AiLex Writes It for You' },
    { id: '17', key: 'solutions.document_drafting.description', category: 'solutions', description: 'Document drafting solution description', defaultContent: 'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.' },

    // Features Section
    { id: '18', key: 'features.title', category: 'features', description: 'Features section title', defaultContent: 'What AiLex Does for Solo Lawyers & Small Firms' },
    { id: '19', key: 'features.research.title', category: 'features', description: 'Research module title', defaultContent: 'Research Module' },
    { id: '20', key: 'features.research.description', category: 'features', description: 'Research module description', defaultContent: 'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.' },
    { id: '21', key: 'features.drafting.title', category: 'features', description: 'Drafting module title', defaultContent: 'Drafting Module' },
    { id: '22', key: 'features.drafting.description', category: 'features', description: 'Drafting module description', defaultContent: 'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.' },
    { id: '23', key: 'features.intake.title', category: 'features', description: 'Intake module title', defaultContent: 'Intake Module' },
    { id: '24', key: 'features.intake.description', category: 'features', description: 'Intake module description', defaultContent: 'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.' }
  ];

  // Apply filtering based on query parameters
  let filteredKeys = mockKeys;

  // Filter by country visibility
  if (country) {
    filteredKeys = filteredKeys.filter(key => {
      // Check if key should be hidden in this country
      if (key.hideInCountries && key.hideInCountries.includes(country)) {
        return false;
      }

      // Check if key should be shown in this country
      if (key.showInCountries && key.showInCountries.length > 0) {
        return key.showInCountries.includes(country);
      }

      // Default visibility based on content type
      switch (key.contentType) {
        case 'us-only':
          return country === 'US';
        case 'belgium-only':
          return country === 'BE';
        case 'shared':
        case 'country-specific':
        default:
          return true;
      }
    });
  }

  // Filter by content type
  if (contentType) {
    filteredKeys = filteredKeys.filter(key => key.contentType === contentType);
  }

  // Filter by category
  if (category && category !== 'all') {
    filteredKeys = filteredKeys.filter(key => key.category === category);
  }

  console.log(`📊 Filtered ${filteredKeys.length} keys from ${mockKeys.length} total (country: ${country}, contentType: ${contentType}, category: ${category})`);

  return res.status(200).json({ keys: filteredKeys });
});

// Mock translations endpoint
app.get('/api/translations', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations`);

  // Return stored translations
  return res.status(200).json({ translations: storedTranslations });
});

// Mock create translation key endpoint
app.post('/api/translations/keys', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/keys`, req.body);

  const {
    key,
    category,
    description,
    defaultContent,
    showInCountries = [],
    hideInCountries = [],
    contentType = 'shared',
    metadata = {}
  } = req.body;

  // Validate required fields
  if (!key || !defaultContent) {
    return res.status(400).json({ error: 'Key and defaultContent are required' });
  }

  // Create new translation key
  const newKey = {
    id: `mock-key-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    key,
    category: category || 'general',
    description: description || '',
    defaultContent,
    showInCountries,
    hideInCountries,
    contentType,
    metadata,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  return res.status(201).json({ key: newKey });
});

// Mock create translation endpoint
app.post('/api/translations', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations`, req.body);

  const { keyId, language, content, context, isAutoTranslated, confidence } = req.body;

  // Mock successful creation
  const mockTranslation = {
    id: `mock-translation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    keyId,
    language,
    content,
    context,
    isAutoTranslated: isAutoTranslated || false,
    confidence,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // Store the translation
  storedTranslations.push(mockTranslation);

  return res.status(201).json({ translation: mockTranslation });
});

// Mock update translation key endpoint
app.put('/api/translations/keys/:id', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/keys/${req.params.id}`, req.body);

  const keyId = req.params.id;
  const updates = req.body;

  // Mock successful update
  const updatedKey = {
    id: keyId,
    ...updates,
    updated_at: new Date().toISOString()
  };

  return res.status(200).json({ key: updatedKey });
});

// Mock update translation endpoint
app.put('/api/translations/:id', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/${req.params.id}`, req.body);

  const { content } = req.body;
  const translationId = req.params.id;

  // Find and update the stored translation
  const translationIndex = storedTranslations.findIndex(t => t.id === translationId);

  if (translationIndex !== -1) {
    storedTranslations[translationIndex] = {
      ...storedTranslations[translationIndex],
      content,
      updated_at: new Date().toISOString()
    };

    return res.status(200).json({ translation: storedTranslations[translationIndex] });
  } else {
    return res.status(404).json({ error: 'Translation not found' });
  }
});

// Mock visibility validation endpoint
app.post('/api/translations/validate-visibility', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/validate-visibility`, req.body);

  const { showInCountries = [], hideInCountries = [], contentType } = req.body;

  const errors = [];
  const warnings = [];

  // Check for conflicting rules
  const conflicts = showInCountries.filter(country => hideInCountries.includes(country));
  if (conflicts.length > 0) {
    errors.push(`Country appears in both show and hide lists: ${conflicts.join(', ')}`);
  }

  // Check content type consistency
  if (contentType === 'us-only' && showInCountries.includes('BE')) {
    warnings.push('Content type is "us-only" but Belgium is in show list');
  }

  if (contentType === 'belgium-only' && showInCountries.includes('US')) {
    warnings.push('Content type is "belgium-only" but US is in show list');
  }

  // Check for empty visibility rules
  if (showInCountries.length === 0 && hideInCountries.length === 0 && contentType === 'country-specific') {
    warnings.push('Content type is "country-specific" but no visibility rules defined');
  }

  return res.status(200).json({
    valid: errors.length === 0,
    errors,
    warnings
  });
});

// Mock auto-translate endpoint
app.post('/api/translations/auto-translate', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/auto-translate`, req.body);

  const { text, targetLanguage, context, legalContext } = req.body;

  // Comprehensive mock translations based on the target language
  const mockTranslations = {
    fr: {
      'Your AI Legal Associate for Belgian Law—Works While You\'re in Court.': 'Votre Associé Juridique IA pour le Droit Belge—Travaille Pendant que Vous Êtes au Tribunal.',
      'Belgian-specific research, drafting, and practice management tools designed for solo advocates and small law firms.': 'Outils de recherche, rédaction et gestion de cabinet spécifiques à la Belgique, conçus pour les avocats indépendants et petits cabinets.',
      'AiLex can do it ALL for you.': 'AiLex peut TOUT faire pour vous.',
      'Get Started Free': 'Commencer Gratuitement',
      'Watch Demo': 'Voir la Démo',
      '40% lawyer time is unbilled admin.': '40% du temps d\'avocat est de l\'administration non facturée.',
      'The average solo attorney loses nearly half their potential billable hours to administrative tasks.': 'L\'avocat indépendant moyen perd près de la moitié de ses heures facturables potentielles aux tâches administratives.',
      'Missed intake calls cost solos $7k+/yr.': 'Les appels d\'admission manqués coûtent aux indépendants 7k$+/an.',
      'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.': 'Chaque appel manqué est un client potentiel perdu. Les petits cabinets sans personnel d\'accueil perdent des milliers annuellement.',
      'Big-law software starts at $199/user.': 'Les logiciels de grands cabinets commencent à 199$/utilisateur.',
      'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.': 'Les solutions juridiques d\'entreprise sont tarifées pour les grands cabinets, les rendant inabordables pour les praticiens indépendants.',
      'Never Miss a Lead — AiLex Answers Every Call': 'Ne Manquez Jamais un Prospect — AiLex Répond à Chaque Appel',
      'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.': 'Le Réceptionniste IA d\'AiLex répond instantanément aux appels entrants, capture le nom du client, le type d\'affaire, l\'urgence, et classe tout dans votre tableau de bord.',
      'Never Miss a Deadline — AiLex Keeps Every Case on Track': 'Ne Manquez Jamais une Échéance — AiLex Garde Chaque Affaire sur la Bonne Voie',
      'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.': 'AiLex trie automatiquement vos documents et échéances, signale les éléments urgents, et suggère les prochaines étapes — pour que vous sachiez toujours ce qui compte le plus.',
      'Never Redraft a Motion — AiLex Writes It for You': 'Ne Rédigez Plus Jamais une Requête — AiLex l\'Écrit pour Vous',
      'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.': 'AiLex trouve les affaires pertinentes, fait ressortir les statuts clés, et rédige des mémos ou requêtes—comme un associé junior, sans les frais généraux.',
      'What AiLex Does for Solo Lawyers & Small Firms': 'Ce qu\'AiLex Fait pour les Avocats Indépendants et Petits Cabinets',
      'Research Module': 'Module de Recherche',
      'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.': 'AiLex télécharge vos affaires, statuts, ou mémoires — puis trouve les citations, crée des résumés, et extrait les arguments clés en secondes.',
      'Drafting Module': 'Module de Rédaction',
      'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.': 'AiLex rédige des requêtes, mémoires, et contrats en utilisant le style de votre cabinet et les règles du tribunal local.',
      'Intake Module': 'Module d\'Admission',
      'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.': 'AiLex répond aux appels, planifie les consultations, et pré-qualifie les prospects pendant que vous vous concentrez sur le travail facturable.'
    },
    nl: {
      'Your AI Legal Associate for Belgian Law—Works While You\'re in Court.': 'Uw AI Juridische Medewerker voor Belgisch Recht—Werkt Terwijl U in de Rechtbank Bent.',
      'Belgian-specific research, drafting, and practice management tools designed for solo advocates and small law firms.': 'Belgische-specifieke onderzoek-, concept- en praktijkbeheertools ontworpen voor solo-advocaten en kleine advocatenkantoren.',
      'AiLex can do it ALL for you.': 'AiLex kan het ALLEMAAL voor u doen.',
      'Get Started Free': 'Gratis Beginnen',
      'Watch Demo': 'Demo Bekijken',
      '40% lawyer time is unbilled admin.': '40% advocaattijd is niet-gefactureerde administratie.',
      'The average solo attorney loses nearly half their potential billable hours to administrative tasks.': 'De gemiddelde solo-advocaat verliest bijna de helft van hun potentiële factureerbare uren aan administratieve taken.',
      'Missed intake calls cost solos $7k+/yr.': 'Gemiste intake-oproepen kosten solo\'s $7k+/jaar.',
      'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.': 'Elke gemiste oproep is een potentiële cliënt weg. Kleine kantoren zonder receptiepersoneel verliezen jaarlijks duizenden.',
      'Big-law software starts at $199/user.': 'Grote-kantoor software begint bij $199/gebruiker.',
      'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.': 'Ondernemingsjuridische oplossingen zijn geprijsd voor grote kantoren, waardoor ze onbetaalbaar zijn voor solo-praktijken.',
      'Never Miss a Lead — AiLex Answers Every Call': 'Mis Nooit een Lead — AiLex Beantwoordt Elke Oproep',
      'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.': 'AiLex\'s AI Receptionist beantwoordt inkomende oproepen onmiddellijk, legt de naam van de cliënt, zaaktype, urgentie vast, en archiveert alles in uw dashboard.',
      'Never Miss a Deadline — AiLex Keeps Every Case on Track': 'Mis Nooit een Deadline — AiLex Houdt Elke Zaak op Koers',
      'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.': 'AiLex sorteert automatisch uw documenten en deadlines, markeert urgente items, en stelt volgende stappen voor — zodat u altijd weet wat het belangrijkst is.',
      'Never Redraft a Motion — AiLex Writes It for You': 'Herschrijf Nooit een Verzoekschrift — AiLex Schrijft Het voor U',
      'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.': 'AiLex vindt relevante zaken, brengt belangrijke statuten naar boven, en stelt memo\'s of verzoekschriften op—zoals een junior medewerker, minus de overhead.',
      'What AiLex Does for Solo Lawyers & Small Firms': 'Wat AiLex Doet voor Solo-Advocaten en Kleine Kantoren',
      'Research Module': 'Onderzoeksmodule',
      'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.': 'AiLex uploadt uw zaken, statuten, of dossiers — vindt dan citaten, maakt samenvattingen, en extraheert belangrijke argumenten in seconden.',
      'Drafting Module': 'Conceptmodule',
      'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.': 'AiLex schrijft verzoekschriften, dossiers, en contracten met behulp van uw kantoor\'s stijl en lokale rechtbankregels.',
      'Intake Module': 'Intakemodule',
      'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.': 'AiLex beantwoordt oproepen, plant consultaties, en pre-kwalificeert leads terwijl u zich richt op factureerbaar werk.'
    }
  };

  const translatedText = mockTranslations[targetLanguage]?.[text] || `[${targetLanguage.toUpperCase()} TRANSLATION] ${text}`;

  // Mock successful auto-translation
  return res.status(200).json({
    translatedText,
    confidence: 0.95,
    suggestions: [
      translatedText,
      `[Alternative] ${translatedText}`,
      `[Formal] ${translatedText}`
    ]
  });
});

// Bulk auto-translate endpoint
app.post('/api/translations/bulk-auto-translate', (req, res) => {
  console.log(`📡 API Request: ${req.method} /api/translations/bulk-auto-translate`, req.body);

  const { keyIds, targetLanguage } = req.body;

  if (!keyIds || !Array.isArray(keyIds) || !targetLanguage) {
    return res.status(400).json({ error: 'Invalid request. keyIds array and targetLanguage are required.' });
  }

  // Get the mock keys to find the content to translate
  const mockKeys = [
    { id: '1', key: 'hero.title.belgium', defaultContent: 'Your AI Legal Associate for Belgian Law—Works While You\'re in Court.' },
    { id: '2', key: 'hero.subtitle.belgium', defaultContent: 'Belgian-specific research, drafting, and practice management tools designed for solo advocates and small law firms.' },
    { id: '3', key: 'hero.tagline', defaultContent: 'AiLex can do it ALL for you.' },
    { id: '4', key: 'hero.cta_primary', defaultContent: 'Get Started Free' },
    { id: '5', key: 'hero.cta_secondary', defaultContent: 'Watch Demo' },
    { id: '6', key: 'problems.admin_time.title', defaultContent: '40% lawyer time is unbilled admin.' },
    { id: '7', key: 'problems.admin_time.description', defaultContent: 'The average solo attorney loses nearly half their potential billable hours to administrative tasks.' },
    { id: '8', key: 'problems.missed_calls.title', defaultContent: 'Missed intake calls cost solos $7k+/yr.' },
    { id: '9', key: 'problems.missed_calls.description', defaultContent: 'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.' },
    { id: '10', key: 'problems.expensive_software.title', defaultContent: 'Big-law software starts at $199/user.' },
    { id: '11', key: 'problems.expensive_software.description', defaultContent: 'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.' },
    { id: '12', key: 'solutions.receptionist.title', defaultContent: 'Never Miss a Lead — AiLex Answers Every Call' },
    { id: '13', key: 'solutions.receptionist.description', defaultContent: 'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.' },
    { id: '14', key: 'solutions.case_management.title', defaultContent: 'Never Miss a Deadline — AiLex Keeps Every Case on Track' },
    { id: '15', key: 'solutions.case_management.description', defaultContent: 'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.' },
    { id: '16', key: 'solutions.document_drafting.title', defaultContent: 'Never Redraft a Motion — AiLex Writes It for You' },
    { id: '17', key: 'solutions.document_drafting.description', defaultContent: 'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.' },
    { id: '18', key: 'features.title', defaultContent: 'What AiLex Does for Solo Lawyers & Small Firms' },
    { id: '19', key: 'features.research.title', defaultContent: 'Research Module' },
    { id: '20', key: 'features.research.description', defaultContent: 'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.' },
    { id: '21', key: 'features.drafting.title', defaultContent: 'Drafting Module' },
    { id: '22', key: 'features.drafting.description', defaultContent: 'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.' },
    { id: '23', key: 'features.intake.title', defaultContent: 'Intake Module' },
    { id: '24', key: 'features.intake.description', defaultContent: 'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.' }
  ];

  // Get comprehensive translations for the requested keys
  const bulkMockTranslations = {
    fr: {
      'Your AI Legal Assistant for Modern Law Practice': 'Votre Assistant Juridique IA pour la Pratique Juridique Moderne',
      'AI-powered legal research, drafting, and practice management tools designed for solo practitioners and small law firms.': 'Outils de recherche juridique, de rédaction et de gestion de cabinet alimentés par l\'IA, conçus pour les praticiens indépendants et les petits cabinets d\'avocats.',
      'Certified by the Belgian Bar Association for legal technology compliance.': 'Certifié par l\'Ordre des Avocats Belges pour la conformité technologique juridique.',
      'Licensed to practice in Texas, Florida, and New York.': 'Autorisé à exercer au Texas, en Floride et à New York.',
      'Welcome {{name}} to {{company}}! We\'re excited to help your practice grow.': 'Bienvenue {{name}} chez {{company}} ! Nous sommes ravis d\'aider votre cabinet à se développer.',
      'Your AI Legal Associate for Belgian Law—Works While You\'re in Court.': 'Votre Associé Juridique IA pour le Droit Belge—Travaille Pendant que Vous Êtes au Tribunal.',
      'Get Started Free': 'Commencer Gratuitement',
      'Watch Demo': 'Voir la Démo',
      '40% lawyer time is unbilled admin.': '40% du temps d\'avocat est de l\'administration non facturée.',
      'The average solo attorney loses nearly half their potential billable hours to administrative tasks.': 'L\'avocat indépendant moyen perd près de la moitié de ses heures facturables potentielles aux tâches administratives.',
      'Missed intake calls cost solos $7k+/yr.': 'Les appels d\'admission manqués coûtent aux indépendants 7k$+/an.',
      'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.': 'Chaque appel manqué est un client potentiel perdu. Les petits cabinets sans personnel d\'accueil perdent des milliers annuellement.',
      'Big-law software starts at $199/user.': 'Les logiciels de grands cabinets commencent à 199$/utilisateur.',
      'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.': 'Les solutions juridiques d\'entreprise sont tarifées pour les grands cabinets, les rendant inabordables pour les praticiens indépendants.',
      'Never Miss a Lead — AiLex Answers Every Call': 'Ne Manquez Jamais un Prospect — AiLex Répond à Chaque Appel',
      'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.': 'Le Réceptionniste IA d\'AiLex répond instantanément aux appels entrants, capture le nom du client, le type d\'affaire, l\'urgence, et classe tout dans votre tableau de bord.',
      'Never Miss a Deadline — AiLex Keeps Every Case on Track': 'Ne Manquez Jamais une Échéance — AiLex Garde Chaque Affaire sur la Bonne Voie',
      'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.': 'AiLex trie automatiquement vos documents et échéances, signale les éléments urgents, et suggère les prochaines étapes — pour que vous sachiez toujours ce qui compte le plus.',
      'Never Redraft a Motion — AiLex Writes It for You': 'Ne Rédigez Plus Jamais une Requête — AiLex l\'Écrit pour Vous',
      'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.': 'AiLex trouve les affaires pertinentes, fait ressortir les statuts clés, et rédige des mémos ou requêtes—comme un associé junior, sans les frais généraux.',
      'What AiLex Does for Solo Lawyers & Small Firms': 'Ce qu\'AiLex Fait pour les Avocats Indépendants et Petits Cabinets',
      'Research Module': 'Module de Recherche',
      'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.': 'AiLex télécharge vos affaires, statuts, ou mémoires — puis trouve les citations, crée des résumés, et extrait les arguments clés en secondes.',
      'Drafting Module': 'Module de Rédaction',
      'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.': 'AiLex rédige des requêtes, mémoires, et contrats en utilisant le style de votre cabinet et les règles du tribunal local.',
      'Intake Module': 'Module d\'Admission',
      'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.': 'AiLex répond aux appels, planifie les consultations, et pré-qualifie les prospects pendant que vous vous concentrez sur le travail facturable.'
    },
    nl: {
      // New test translations
      'Your AI Legal Assistant for Modern Law Practice': 'Uw AI Juridische Assistent voor Moderne Rechtspraktijk',
      'AI-powered legal research, drafting, and practice management tools designed for solo practitioners and small law firms.': 'AI-aangedreven juridisch onderzoek, concepten en praktijkbeheertools ontworpen voor solo-praktijken en kleine advocatenkantoren.',
      'Certified by the Belgian Bar Association for legal technology compliance.': 'Gecertificeerd door de Belgische Orde van Advocaten voor juridische technologie compliance.',
      'Licensed to practice in Texas, Florida, and New York.': 'Bevoegd om te praktiseren in Texas, Florida en New York.',
      'Welcome {{name}} to {{company}}! We\'re excited to help your practice grow.': 'Welkom {{name}} bij {{company}}! We zijn enthousiast om uw praktijk te helpen groeien.',

      // Original translations
      'Your AI Legal Associate for Belgian Law—Works While You\'re in Court.': 'Uw AI Juridische Medewerker voor Belgisch Recht—Werkt Terwijl U in de Rechtbank Bent.',
      'Belgian-specific research, drafting, and practice management tools designed for solo advocates and small law firms.': 'Belgische-specifieke onderzoek-, concept- en praktijkbeheertools ontworpen voor solo-advocaten en kleine advocatenkantoren.',
      'AiLex can do it ALL for you.': 'AiLex kan het ALLEMAAL voor u doen.',
      'Get Started Free': 'Gratis Beginnen',
      'Watch Demo': 'Demo Bekijken',
      '40% lawyer time is unbilled admin.': '40% advocaattijd is niet-gefactureerde administratie.',
      'The average solo attorney loses nearly half their potential billable hours to administrative tasks.': 'De gemiddelde solo-advocaat verliest bijna de helft van hun potentiële factureerbare uren aan administratieve taken.',
      'Missed intake calls cost solos $7k+/yr.': 'Gemiste intake-oproepen kosten solo\'s $7k+/jaar.',
      'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.': 'Elke gemiste oproep is een potentiële cliënt weg. Kleine kantoren zonder receptiepersoneel verliezen jaarlijks duizenden.',
      'Big-law software starts at $199/user.': 'Grote-kantoor software begint bij $199/gebruiker.',
      'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.': 'Ondernemingsjuridische oplossingen zijn geprijsd voor grote kantoren, waardoor ze onbetaalbaar zijn voor solo-praktijken.',
      'Never Miss a Lead — AiLex Answers Every Call': 'Mis Nooit een Lead — AiLex Beantwoordt Elke Oproep',
      'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.': 'AiLex\'s AI Receptionist beantwoordt inkomende oproepen onmiddellijk, legt de naam van de cliënt, zaaktype, urgentie vast, en archiveert alles in uw dashboard.',
      'Never Miss a Deadline — AiLex Keeps Every Case on Track': 'Mis Nooit een Deadline — AiLex Houdt Elke Zaak op Koers',
      'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.': 'AiLex sorteert automatisch uw documenten en deadlines, markeert urgente items, en stelt volgende stappen voor — zodat u altijd weet wat het belangrijkst is.',
      'Never Redraft a Motion — AiLex Writes It for You': 'Herschrijf Nooit een Verzoekschrift — AiLex Schrijft Het voor U',
      'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.': 'AiLex vindt relevante zaken, brengt belangrijke statuten naar boven, en stelt memo\'s of verzoekschriften op—zoals een junior medewerker, minus de overhead.',
      'What AiLex Does for Solo Lawyers & Small Firms': 'Wat AiLex Doet voor Solo-Advocaten en Kleine Kantoren',
      'Research Module': 'Onderzoeksmodule',
      'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.': 'AiLex uploadt uw zaken, statuten, of dossiers — vindt dan citaten, maakt samenvattingen, en extraheert belangrijke argumenten in seconden.',
      'Drafting Module': 'Conceptmodule',
      'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.': 'AiLex schrijft verzoekschriften, dossiers, en contracten met behulp van uw kantoor\'s stijl en lokale rechtbankregels.',
      'Intake Module': 'Intakemodule',
      'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.': 'AiLex beantwoordt oproepen, plant consultaties, en pre-kwalificeert leads terwijl u zich richt op factureerbaar werk.'
    }
  };

  const results = keyIds.map(keyId => {
    const key = mockKeys.find(k => k.id === keyId);
    if (!key) {
      return { keyId, error: 'Key not found' };
    }

    const translatedText = bulkMockTranslations[targetLanguage]?.[key.defaultContent] || `[${targetLanguage.toUpperCase()} TRANSLATION] ${key.defaultContent}`;

    return {
      keyId,
      translatedText,
      confidence: 0.95,
      success: true
    };
  });

  return res.status(200).json({
    results,
    summary: {
      total: keyIds.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => r.error).length
    }
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Development API server running on http://localhost:${PORT}`);
  console.log(`📡 CORS enabled for http://localhost:5174`);
  console.log(`🔐 Mock admin login: <EMAIL> / admin123`);
});
