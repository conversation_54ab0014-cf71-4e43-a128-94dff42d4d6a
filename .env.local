# Local Development Environment Variables

# Supabase Configuration (REQUIRED for prospect management)
# Note: Using SUPABASE_KEY as the service role key
SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzc5NjI0NiwiZXhwIjoyMDUzMzcyMjQ2fQ.XufCiYJLa531SyHDNkkVFNH0_rbPgZp4UG5yMGRabfs

# Resend Email Configuration (REQUIRED for email verification)
RESEND_API_KEY=re_BRaTQns1_HH3gkuPAWrcjSSgAbSuDRotB

# Website Configuration
VITE_SITE_URL=http://localhost:3000

# PI Lawyer AI Backend Integration (NOT NEEDED FOR TEMPORARY APPROACH)
# VITE_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app
# NEXT_PUBLIC_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app

# Turnstile Bot Protection (Your Keys)
VITE_TURNSTILE_SITE_KEY=0x4AAAAAAA7TWjsvWoeed9DQ
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA7TWjsvWoeed9DQ
TURNSTILE_SECRET_KEY=0x4AAAAAAA7TWk3iQhOJVytd19wCcp3_QGI

# Environment
NODE_ENV=development

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-in-production-2024

# Gemini API Key
GEMINI_API_KEY=AIzaSyDdcoARS1sq9DlNXKFLi6RMY94JzDhNxhI

# PI Lawyer AI Backend Integration
VITE_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app
NEXT_PUBLIC_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app
