import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { Resend } from "resend";
import crypto from "crypto";
import { z } from "zod";

// Environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const jwtSecret = process.env.JWT_SECRET || "your-secret-key-change-in-production";
const resendApiKey = process.env.RESEND_API_KEY;
const siteUrl = process.env.VITE_SITE_URL || "http://localhost:3000";

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error("Missing Supabase environment variables");
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);
const resend = resendApiKey ? new Resend(resendApiKey) : null;

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
});

const forgotPasswordSchema = z.object({
  email: z.string().email(),
});

const resetPasswordSchema = z.object({
  token: z.string().min(1),
  newPassword: z.string().min(8),
});

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Cookie",
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  // Route based on URL path
  const url = new URL(req.url!, `http://${req.headers.host}`);
  const action = url.searchParams.get('action');

  try {
    switch (action) {
      case 'login':
        return await handleLogin(req, res);
      case 'logout':
        return await handleLogout(req, res);
      case 'verify':
        return await handleVerify(req, res);
      case 'forgot-password':
        return await handleForgotPassword(req, res);
      case 'reset-password':
        return await handleResetPassword(req, res);
      default:
        return res.status(400).json({ error: "Invalid action parameter" });
    }
  } catch (error) {
    console.error("Admin auth error:", error);
    return res.status(500).json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

// Login handler
async function handleLogin(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const validation = loginSchema.safeParse(req.body);
  
  if (!validation.success) {
    return res.status(400).json({ 
      error: "Invalid input",
      details: validation.error.errors
    });
  }

  const { email, password } = validation.data;

  // Find website admin user by email
  const { data: adminUser, error } = await supabase
    .from("website_admins")
    .select("*")
    .eq("email", email)
    .eq("is_active", "true")
    .single();

  if (error || !adminUser) {
    return res.status(401).json({ error: "Invalid credentials" });
  }

  // Verify password
  const isValidPassword = await bcrypt.compare(password, adminUser.password_hash);
  
  if (!isValidPassword) {
    return res.status(401).json({ error: "Invalid credentials" });
  }

  // Update last login
  await supabase
    .from("website_admins")
    .update({ last_login_at: new Date().toISOString() })
    .eq("id", adminUser.id);

  // Generate JWT token
  const token = jwt.sign(
    { 
      userId: adminUser.id, 
      email: adminUser.email,
      name: adminUser.name
    },
    jwtSecret,
    { expiresIn: "24h" }
  );

  // Set HTTP-only cookie
  res.setHeader("Set-Cookie", [
    `admin_token=${token}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict${
      process.env.NODE_ENV === "production" ? "; Secure" : ""
    }`
  ]);

  return res.status(200).json({
    success: true,
    user: {
      id: adminUser.id,
      email: adminUser.email,
      name: adminUser.name,
    }
  });
}

// Logout handler
async function handleLogout(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Clear the admin token cookie
  res.setHeader("Set-Cookie", [
    `admin_token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict${
      process.env.NODE_ENV === "production" ? "; Secure" : ""
    }`
  ]);

  return res.status(200).json({ success: true });
}

// Verify handler
async function handleVerify(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Extract token from cookie
  const cookies = req.headers.cookie;
  let token = null;

  if (cookies) {
    const tokenMatch = cookies.match(/admin_token=([^;]+)/);
    token = tokenMatch ? tokenMatch[1] : null;
  }

  if (!token) {
    return res.status(401).json({ error: "No authentication token" });
  }

  // Verify JWT token
  const decoded = jwt.verify(token, jwtSecret) as any;

  return res.status(200).json({
    success: true,
    user: {
      id: decoded.userId,
      email: decoded.email,
      name: decoded.name,
    }
  });
}

// Forgot password handler
async function handleForgotPassword(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const validation = forgotPasswordSchema.safeParse(req.body);
  
  if (!validation.success) {
    return res.status(400).json({ 
      error: "Invalid input",
      details: validation.error.errors
    });
  }

  const { email } = validation.data;

  // Find website admin user by email
  const { data: adminUser, error } = await supabase
    .from("website_admins")
    .select("*")
    .eq("email", email)
    .eq("is_active", "true")
    .single();

  // Always return success to prevent email enumeration
  if (error || !adminUser) {
    return res.status(200).json({ 
      success: true, 
      message: "If an account with that email exists, a password reset link has been sent." 
    });
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString("hex");
  const resetExpires = new Date(Date.now() + 3600000); // 1 hour from now

  // Save reset token to database
  await supabase
    .from("website_admins")
    .update({
      password_reset_token: resetToken,
      password_reset_expires: resetExpires.toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq("id", adminUser.id);

  // Send reset email if Resend is configured
  if (resend) {
    const resetUrl = `${siteUrl}/admin-secret-portal/reset-password?token=${resetToken}`;
    
    try {
      await resend.emails.send({
        from: "AiLex Admin <<EMAIL>>",
        to: [email],
        subject: "AiLex Admin - Password Reset Request",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #1f2937;">Password Reset Request</h2>
            <p>Hello ${adminUser.name},</p>
            <p>You requested a password reset for your AiLex admin account. Click the link below to reset your password:</p>
            <div style="margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Reset Password
              </a>
            </div>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this password reset, please ignore this email.</p>
          </div>
        `,
      });
    } catch (emailError) {
      console.error("Failed to send reset email:", emailError);
    }
  }

  return res.status(200).json({ 
    success: true, 
    message: "If an account with that email exists, a password reset link has been sent." 
  });
}

// Reset password handler
async function handleResetPassword(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const validation = resetPasswordSchema.safeParse(req.body);
  
  if (!validation.success) {
    return res.status(400).json({ 
      error: "Invalid input",
      details: validation.error.errors
    });
  }

  const { token, newPassword } = validation.data;

  // Find website admin user by reset token
  const { data: adminUser, error } = await supabase
    .from("website_admins")
    .select("*")
    .eq("password_reset_token", token)
    .eq("is_active", "true")
    .single();

  if (error || !adminUser) {
    return res.status(400).json({ error: "Invalid or expired reset token" });
  }

  // Check if token is expired
  const now = new Date();
  const expiresAt = new Date(adminUser.password_reset_expires);
  
  if (now > expiresAt) {
    return res.status(400).json({ error: "Reset token has expired" });
  }

  // Hash new password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(newPassword, saltRounds);

  // Update password and clear reset token
  const { error: updateError } = await supabase
    .from("website_admins")
    .update({
      password_hash: passwordHash,
      password_reset_token: null,
      password_reset_expires: null,
      updated_at: new Date().toISOString(),
    })
    .eq("id", adminUser.id);

  if (updateError) {
    console.error("Failed to update password:", updateError);
    return res.status(500).json({ error: "Failed to update password" });
  }

  return res.status(200).json({ 
    success: true, 
    message: "Password has been reset successfully" 
  });
}
