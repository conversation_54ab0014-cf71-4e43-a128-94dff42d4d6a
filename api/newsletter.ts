import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import { Resend } from "resend";
import { z } from "zod";

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  // Check environment variables early
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error("Missing Supabase configuration:", {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseKey,
    });
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing database configuration",
    });
  }

  try {
    // Validate request body
    const { email } = newsletterSchema.parse(req.body);

    // Initialize Supabase client (already validated above)
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check if email already exists
    const { data: existing } = await supabase
      .from("contact_submissions")
      .select("email")
      .eq("email", email)
      .single();

    if (existing) {
      return res.status(200).json({
        success: true,
        message: "You are already subscribed to our newsletter",
      });
    }

    // Store in Supabase (newsletter signups go to same table with null name/message)
    const { error: dbError } = await supabase
      .from("contact_submissions")
      .insert({ email, name: null, message: null });

    if (dbError) {
      console.error("Database error:", dbError);
      return res.status(500).json({ error: "Failed to save subscription" });
    }

    // Send welcome email
    const resendApiKey = process.env.RESEND_API_KEY;
    if (resendApiKey) {
      try {
        const resend = new Resend(resendApiKey);
        await resend.emails.send({
          from: "AiLex <<EMAIL>>",
          to: email,
          subject: "Welcome to AiLex Newsletter!",
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1EAEDB;">Welcome to AiLex!</h2>
              <p>Thanks for subscribing to our newsletter!</p>
              <p>You'll receive weekly AI tips and insights to help your legal practice grow.</p>
              <p>Stay tuned for:</p>
              <ul>
                <li>Latest AI tools for lawyers</li>
                <li>Practice efficiency tips</li>
                <li>Legal tech trends</li>
                <li>AiLex product updates</li>
              </ul>
              <p>Best regards,<br>The AiLex Team</p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">
                You can unsubscribe at any time by replying to any of our emails.
              </p>
            </div>
          `,
        });
      } catch (emailError) {
        console.error("Email error:", emailError);
        // Don't fail the request if email fails
      }
    }

    res.status(200).json({
      success: true,
      message: "Successfully subscribed to newsletter",
    });
  } catch (error) {
    console.error("Newsletter API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid email address",
        details: error.format(),
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
