import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";

// Environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY; // Use anon key for public access

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Validation schema
const getTranslationsSchema = z.object({
  language: z.enum(["en", "fr", "nl"]).optional(),
  category: z.string().optional(),
  keys: z.array(z.string()).optional(), // Specific keys to fetch
});

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type",
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const validation = getTranslationsSchema.safeParse(req.query);
    
    if (!validation.success) {
      return res.status(400).json({ 
        error: "Invalid query parameters",
        details: validation.error.errors
      });
    }

    const { language = "en", category, keys } = validation.data;

    // Build the query
    let query = supabase
      .from("translation_keys")
      .select(`
        id,
        key,
        category,
        default_content,
        translations!inner (
          language,
          content,
          is_auto_translated,
          confidence
        )
      `);

    // Filter by category if provided
    if (category) {
      query = query.eq("category", category);
    }

    // Filter by specific keys if provided
    if (keys && keys.length > 0) {
      query = query.in("key", keys);
    }

    // Filter by language
    query = query.eq("translations.language", language);

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching public translations:", error);
      return res.status(500).json({ error: "Failed to fetch translations" });
    }

    // Transform the data into a more usable format
    const translations: Record<string, {
      content: string;
      isAutoTranslated: boolean;
      confidence?: number;
    }> = {};

    data?.forEach((item) => {
      const translation = item.translations[0]; // Should only be one per language
      translations[item.key] = {
        content: translation?.content || item.default_content,
        isAutoTranslated: translation?.is_auto_translated === "true",
        confidence: translation?.confidence ? parseFloat(translation.confidence) : undefined,
      };
    });

    // Set cache headers for better performance
    res.setHeader("Cache-Control", "public, s-maxage=300, stale-while-revalidate=600");

    return res.status(200).json({ 
      language,
      translations,
      count: Object.keys(translations).length
    });

  } catch (error) {
    console.error("Public translations API error:", error);
    return res.status(500).json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
