import { VercelRequest, VercelResponse } from "@vercel/node";
import { z } from "zod";

// Environment variables
const geminiApiKey = process.env.GEMINI_API_KEY;

if (!geminiApiKey) {
  console.warn("GEMINI_API_KEY not found - auto-translation will be disabled");
}

// Validation schema
const translateRequestSchema = z.object({
  text: z.string().min(1),
  targetLanguage: z.enum(["fr", "nl"]),
  context: z.string().optional(),
  legalContext: z.boolean().optional().default(false),
});

// Language mappings
const languageNames = {
  fr: "French",
  nl: "Dutch",
};

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  if (!geminiApiKey) {
    return res.status(503).json({ 
      error: "Auto-translation service unavailable",
      details: "Gemini API key not configured"
    });
  }

  try {
    const validation = translateRequestSchema.safeParse(req.body);
    
    if (!validation.success) {
      return res.status(400).json({ 
        error: "Invalid input",
        details: validation.error.errors
      });
    }

    const { text, targetLanguage, context, legalContext } = validation.data;

    // Build the prompt for Gemini
    const prompt = buildTranslationPrompt(text, targetLanguage, context, legalContext);

    // Call Gemini API
    const translationResult = await callGeminiAPI(prompt);

    return res.status(200).json({
      translatedText: translationResult.translatedText,
      confidence: translationResult.confidence,
      suggestions: translationResult.suggestions,
    });

  } catch (error) {
    console.error("Auto-translation error:", error);
    return res.status(500).json({ 
      error: "Translation failed",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

function buildTranslationPrompt(
  text: string, 
  targetLanguage: "fr" | "nl", 
  context?: string, 
  legalContext?: boolean
): string {
  const targetLangName = languageNames[targetLanguage];
  
  let prompt = `You are a professional translator specializing in legal and business content. `;
  
  if (legalContext) {
    prompt += `This is legal content for a Belgian law firm's website. Use appropriate legal terminology and formal language. `;
  }
  
  prompt += `Translate the following English text to ${targetLangName}:\n\n`;
  prompt += `"${text}"\n\n`;
  
  if (context) {
    prompt += `Context: ${context}\n\n`;
  }
  
  if (targetLanguage === "fr") {
    prompt += `Use Belgian French (not French from France). `;
  } else if (targetLanguage === "nl") {
    prompt += `Use Belgian Dutch (Flemish). `;
  }
  
  prompt += `Requirements:
- Maintain the same tone and style as the original
- Keep any HTML tags or special formatting intact
- Use professional, clear language appropriate for a legal services website
- If there are multiple possible translations, choose the most appropriate for Belgian legal context

Please respond with only the translated text, no explanations or additional commentary.`;

  return prompt;
}

async function callGeminiAPI(prompt: string): Promise<{
  translatedText: string;
  confidence: number;
  suggestions: string[];
}> {
  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${geminiApiKey}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
        generationConfig: {
          temperature: 0.3, // Lower temperature for more consistent translations
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
        ],
      }),
    }
  );

  if (!response.ok) {
    const errorData = await response.text();
    throw new Error(`Gemini API error: ${response.status} - ${errorData}`);
  }

  const data = await response.json();

  if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
    throw new Error("Invalid response from Gemini API");
  }

  const translatedText = data.candidates[0].content.parts[0].text.trim();
  
  // Calculate a simple confidence score based on response quality
  const confidence = calculateConfidence(translatedText);

  return {
    translatedText,
    confidence,
    suggestions: [], // Could be enhanced to provide alternative translations
  };
}

function calculateConfidence(translatedText: string): number {
  // Simple confidence calculation based on text characteristics
  let confidence = 0.8; // Base confidence
  
  // Reduce confidence if text seems incomplete or has issues
  if (translatedText.length < 3) {
    confidence -= 0.3;
  }
  
  if (translatedText.includes("...") || translatedText.includes("[")) {
    confidence -= 0.2;
  }
  
  // Increase confidence for well-formed text
  if (translatedText.length > 10 && !translatedText.includes("?")) {
    confidence += 0.1;
  }
  
  return Math.max(0.1, Math.min(1.0, confidence));
}
