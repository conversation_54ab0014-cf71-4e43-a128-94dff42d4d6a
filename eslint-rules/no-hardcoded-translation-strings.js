/**
 * ESLint rule to prevent hardcoded translation strings
 * Enforces use of t() function for user-facing text
 */

export default {
  meta: {
    type: "problem",
    docs: {
      description: "Prevent hardcoded strings that should use translation system",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [
      {
        type: "object",
        properties: {
          allowedStrings: {
            type: "array",
            items: { type: "string" },
            description: "Array of strings that are allowed to be hardcoded",
          },
          allowedPatterns: {
            type: "array",
            items: { type: "string" },
            description: "Array of regex patterns for allowed strings",
          },
          minLength: {
            type: "number",
            description: "Minimum string length to check (default: 3)",
            default: 3,
          },
        },
        additionalProperties: false,
      },
    ],
    messages: {
      hardcodedString: "Hardcoded string '{{string}}' should use translation system: t('{{suggestedKey}}')",
      hardcodedStringGeneric: "Hardcoded string '{{string}}' should use translation system with t() function",
    },
  },

  create(context) {
    const options = context.options[0] || {};
    const allowedStrings = new Set([
      // Default allowed strings
      "", " ", ".", ",", ":", ";", "!", "?", "-", "+", "=", "/", "\\", "|", "&",
      "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
      "px", "py", "mx", "my", "rem", "em", "%",
      ...(options.allowedStrings || []),
    ]);

    const allowedPatterns = [
      // CSS classes and IDs
      /^[a-z-]+:[a-z-]+$/, // Tailwind modifiers like "hover:bg-blue-500"
      /^[a-z-]+([-_][a-z0-9]+)*$/, // CSS class names
      /^#[a-zA-Z0-9-_]+$/, // IDs
      /^[a-z]+\([^)]*\)$/, // CSS functions like "calc(100% - 20px)"
      /^https?:\/\//, // URLs
      /^\/[a-zA-Z0-9\-_\/]*$/, // Paths
      /^[A-Z_][A-Z0-9_]*$/, // Constants like "API_URL"
      ...(options.allowedPatterns || []).map(p => new RegExp(p)),
    ];

    const minLength = options.minLength || 3;

    function isAllowedString(str) {
      // Check if string is in allowed list
      if (allowedStrings.has(str)) return true;
      
      // Check if string is too short
      if (str.length < minLength) return true;
      
      // Check against allowed patterns
      return allowedPatterns.some(pattern => pattern.test(str));
    }

    function generateSuggestedKey(str) {
      // Simple key suggestion based on string content
      const words = str.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 0)
        .slice(0, 3); // Take first 3 words
      
      if (words.length === 0) return 'content.text';
      
      return `content.${words.join('_')}`;
    }

    function checkStringLiteral(node, str) {
      if (isAllowedString(str)) return;

      const suggestedKey = generateSuggestedKey(str);
      
      context.report({
        node,
        messageId: "hardcodedString",
        data: {
          string: str.length > 50 ? str.substring(0, 47) + "..." : str,
          suggestedKey,
        },
        fix(fixer) {
          // Auto-fix: wrap in t() function
          return fixer.replaceText(node, `{t('${suggestedKey}')}`);
        },
      });
    }

    return {
      // Check JSX text content
      JSXText(node) {
        const text = node.value.trim();
        if (text) {
          checkStringLiteral(node, text);
        }
      },

      // Check string literals in JSX expressions
      "JSXExpressionContainer > Literal"(node) {
        if (typeof node.value === 'string') {
          checkStringLiteral(node, node.value);
        }
      },

      // Check template literals in JSX
      "JSXExpressionContainer > TemplateLiteral"(node) {
        // Only check simple template literals without expressions
        if (node.expressions.length === 0 && node.quasis.length === 1) {
          const text = node.quasis[0].value.cooked;
          if (text && text.trim()) {
            checkStringLiteral(node, text.trim());
          }
        }
      },

      // Check string literals in specific JSX attributes that might contain user-facing text
      "JSXAttribute[name.name='title'] > Literal"(node) {
        if (typeof node.value === 'string') {
          checkStringLiteral(node, node.value);
        }
      },
      "JSXAttribute[name.name='alt'] > Literal"(node) {
        if (typeof node.value === 'string') {
          checkStringLiteral(node, node.value);
        }
      },
      "JSXAttribute[name.name='placeholder'] > Literal"(node) {
        if (typeof node.value === 'string') {
          checkStringLiteral(node, node.value);
        }
      },
      "JSXAttribute[name.name='aria-label'] > Literal"(node) {
        if (typeof node.value === 'string') {
          checkStringLiteral(node, node.value);
        }
      },
    };
  },
};
