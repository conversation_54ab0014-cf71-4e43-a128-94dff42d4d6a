import js from "@eslint/js";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsparser from "@typescript-eslint/parser";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import globals from "globals";
import noHardcodedTranslationStrings from "./eslint-rules/no-hardcoded-translation-strings.js";

export default [
  js.configs.recommended,
  {
    files: ["client/src/**/*.{ts,tsx}"],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        ...globals.browser,
        ...globals.es2021,
      },
    },
    settings: {
      react: {
        version: "detect",
      },
    },
    plugins: {
      "@typescript-eslint": tseslint,
      "react": react,
      "react-hooks": reactHooks,
      "custom": {
        rules: {
          "no-hardcoded-translation-strings": noHardcodedTranslationStrings,
        },
      },
    },
    rules: {
      ...tseslint.configs.recommended.rules,
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_" },
      ],
      "@typescript-eslint/no-explicit-any": "warn",
      "no-console": "warn",
      "no-undef": "off", // TypeScript handles this

      // React-specific rules
      "react/react-in-jsx-scope": "off", // Not needed with new JSX transform
      "react/prop-types": "off", // Using TypeScript for prop validation

      // HARDCODED STRING PREVENTION RULES
      "react/jsx-no-literals": [
        "error",
        {
          "noStrings": true,
          "allowedStrings": [
            // Allow common single characters and symbols
            " ", ".", ",", ":", ";", "!", "?", "-", "+", "=", "/", "\\", "|",
            // Allow numbers as strings
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            // Allow common CSS classes (these should be in className, not content)
            "px", "py", "mx", "my", "w-", "h-", "text-", "bg-", "border-",
            // Allow empty strings
            "",
          ],
          "ignoreProps": true, // Allow hardcoded strings in props like className, id, etc.
        },
      ],

      // Custom rule for translation strings
      "custom/no-hardcoded-translation-strings": [
        "error",
        {
          "allowedStrings": [
            // Project-specific allowed strings
            "AiLex", "AI", "API", "URL", "ID", "UUID",
          ],
          "minLength": 3,
        },
      ],
    },
  },
  {
    files: ["api/**/*.{ts,tsx}", "shared/**/*.{ts,tsx}"],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
      },
      globals: {
        ...globals.node,
        ...globals.es2021,
      },
    },
    plugins: {
      "@typescript-eslint": tseslint,
    },
    rules: {
      ...tseslint.configs.recommended.rules,
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_" },
      ],
      "@typescript-eslint/no-explicit-any": "warn",
      "no-console": "warn",
    },
  },
  {
    files: ["**/*.test.{ts,tsx}", "**/__tests__/**/*.{ts,tsx}"],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
      },
      globals: {
        ...globals.node,
        ...globals.es2021,
        // Vitest globals
        describe: "readonly",
        it: "readonly",
        test: "readonly",
        expect: "readonly",
        beforeAll: "readonly",
        beforeEach: "readonly",
        afterAll: "readonly",
        afterEach: "readonly",
        vi: "readonly",
      },
    },
    plugins: {
      "@typescript-eslint": tseslint,
    },
    rules: {
      ...tseslint.configs.recommended.rules,
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_" },
      ],
      "@typescript-eslint/no-explicit-any": "warn",
      "no-console": "off", // Allow console in tests
    },
  },
  {
    ignores: ["dist/**", "node_modules/**", "*.js"],
  },
];
