/**
 * Translation Status Service
 * 
 * Provides comprehensive analytics and status information
 * for the translation system dashboard.
 */

import { supabase } from '@/lib/supabase';
import type { Country, Language } from '@/types/translations';

export interface TranslationStats {
  totalKeys: number;
  totalTranslations: number;
  translationsByLanguage: Record<Language, number>;
  translationsByCategory: Record<string, {
    total: number;
    translated: Record<Language, number>;
    coverage: Record<Language, number>;
  }>;
  contentTypeBreakdown: Record<string, number>;
  countrySpecificStats: Record<Country, {
    totalKeys: number;
    translatedKeys: Record<Language, number>;
  }>;
}

export interface MissingTranslation {
  keyId: string;
  key: string;
  category: string;
  contentType: string;
  missingLanguages: Language[];
  priority: 'high' | 'medium' | 'low';
  defaultContent: string;
}

export interface RecentActivity {
  id: string;
  type: 'key_created' | 'translation_added' | 'translation_updated' | 'key_deleted';
  key: string;
  language?: Language;
  timestamp: string;
  user?: string;
  details: string;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  issues: Array<{
    type: 'missing_translations' | 'unused_keys' | 'invalid_keys' | 'empty_content';
    severity: 'high' | 'medium' | 'low';
    count: number;
    message: string;
  }>;
  recommendations: string[];
}

export interface DashboardData {
  stats: TranslationStats;
  missingTranslations: MissingTranslation[];
  recentActivity: RecentActivity[];
  systemHealth: SystemHealth;
  lastUpdated: string;
}

export class TranslationStatusService {
  private static instance: TranslationStatusService;
  
  public static getInstance(): TranslationStatusService {
    if (!TranslationStatusService.instance) {
      TranslationStatusService.instance = new TranslationStatusService();
    }
    return TranslationStatusService.instance;
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const [stats, missingTranslations, recentActivity] = await Promise.all([
        this.getTranslationStats(),
        this.getMissingTranslations(),
        this.getRecentActivity()
      ]);

      const systemHealth = this.calculateSystemHealth(stats, missingTranslations);

      return {
        stats,
        missingTranslations,
        recentActivity,
        systemHealth,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get translation statistics
   */
  async getTranslationStats(): Promise<TranslationStats> {
    // Get all translation keys with categories
    const { data: keys, error: keysError } = await supabase
      .from('translation_keys')
      .select(`
        id,
        key,
        content_type,
        show_in_countries,
        translation_categories(name)
      `);

    if (keysError) throw keysError;

    // Get all translations
    const { data: translations, error: translationsError } = await supabase
      .from('translations')
      .select('key_id, language');

    if (translationsError) throw translationsError;

    // Process statistics
    const translationsByLanguage: Record<Language, number> = { en: 0, fr: 0, nl: 0 };
    const translationsByCategory: Record<string, any> = {};
    const contentTypeBreakdown: Record<string, number> = {};
    const countrySpecificStats: Record<Country, any> = {
      US: { totalKeys: 0, translatedKeys: { en: 0, fr: 0, nl: 0 } },
      BE: { totalKeys: 0, translatedKeys: { en: 0, fr: 0, nl: 0 } }
    };

    // Count translations by language
    translations?.forEach(t => {
      if (t.language in translationsByLanguage) {
        translationsByLanguage[t.language as Language]++;
      }
    });

    // Process keys
    keys?.forEach(key => {
      const category = key.translation_categories?.name || 'uncategorized';
      const contentType = key.content_type || 'shared';
      const countries = key.show_in_countries || ['US', 'BE'];

      // Content type breakdown
      contentTypeBreakdown[contentType] = (contentTypeBreakdown[contentType] || 0) + 1;

      // Category breakdown
      if (!translationsByCategory[category]) {
        translationsByCategory[category] = {
          total: 0,
          translated: { en: 0, fr: 0, nl: 0 },
          coverage: { en: 0, fr: 0, nl: 0 }
        };
      }
      translationsByCategory[category].total++;

      // Count translations for this key
      const keyTranslations = translations?.filter(t => t.key_id === key.id) || [];
      keyTranslations.forEach(t => {
        if (t.language in translationsByCategory[category].translated) {
          translationsByCategory[category].translated[t.language as Language]++;
        }
      });

      // Country-specific stats
      countries.forEach(country => {
        if (country in countrySpecificStats) {
          countrySpecificStats[country as Country].totalKeys++;
          keyTranslations.forEach(t => {
            if (t.language in countrySpecificStats[country].translatedKeys) {
              countrySpecificStats[country].translatedKeys[t.language as Language]++;
            }
          });
        }
      });
    });

    // Calculate coverage percentages
    Object.keys(translationsByCategory).forEach(category => {
      const cat = translationsByCategory[category];
      Object.keys(cat.translated).forEach(lang => {
        cat.coverage[lang] = cat.total > 0 ? (cat.translated[lang] / cat.total) * 100 : 0;
      });
    });

    return {
      totalKeys: keys?.length || 0,
      totalTranslations: translations?.length || 0,
      translationsByLanguage,
      translationsByCategory,
      contentTypeBreakdown,
      countrySpecificStats
    };
  }

  /**
   * Get missing translations
   */
  async getMissingTranslations(): Promise<MissingTranslation[]> {
    const { data: keys, error: keysError } = await supabase
      .from('translation_keys')
      .select(`
        id,
        key,
        content_type,
        default_content,
        translation_categories(name),
        translations(language)
      `);

    if (keysError) throw keysError;

    const missingTranslations: MissingTranslation[] = [];
    const requiredLanguages: Language[] = ['fr', 'nl'];

    keys?.forEach(key => {
      const existingLanguages = new Set(
        key.translations?.map(t => t.language) || []
      );

      const missingLanguages = requiredLanguages.filter(
        lang => !existingLanguages.has(lang)
      );

      if (missingLanguages.length > 0) {
        // Determine priority based on content type
        let priority: 'high' | 'medium' | 'low' = 'medium';
        if (key.content_type === 'belgium-only') priority = 'high';
        if (key.content_type === 'us-only') priority = 'low';

        missingTranslations.push({
          keyId: key.id,
          key: key.key,
          category: key.translation_categories?.name || 'uncategorized',
          contentType: key.content_type || 'shared',
          missingLanguages,
          priority,
          defaultContent: key.default_content || ''
        });
      }
    });

    // Sort by priority and category
    return missingTranslations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return a.category.localeCompare(b.category);
    });
  }

  /**
   * Get recent activity (mock for now)
   */
  async getRecentActivity(): Promise<RecentActivity[]> {
    // In a real implementation, this would come from an audit log table
    return [
      {
        id: '1',
        type: 'key_created',
        key: 'hero.title_belgian',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        details: 'Created Belgian-specific hero title'
      },
      {
        id: '2',
        type: 'translation_added',
        key: 'hero.subtitle_main',
        language: 'fr',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        details: 'Added French translation'
      },
      {
        id: '3',
        type: 'translation_updated',
        key: 'features.research.title',
        language: 'nl',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
        details: 'Updated Dutch translation'
      }
    ];
  }

  /**
   * Calculate system health
   */
  private calculateSystemHealth(
    stats: TranslationStats,
    missingTranslations: MissingTranslation[]
  ): SystemHealth {
    const issues = [];
    const recommendations = [];

    // Check missing translations
    const highPriorityMissing = missingTranslations.filter(m => m.priority === 'high').length;
    const totalMissing = missingTranslations.length;

    if (highPriorityMissing > 0) {
      issues.push({
        type: 'missing_translations' as const,
        severity: 'high' as const,
        count: highPriorityMissing,
        message: `${highPriorityMissing} high-priority translations missing`
      });
      recommendations.push('Prioritize Belgian-specific content translations');
    }

    if (totalMissing > 20) {
      issues.push({
        type: 'missing_translations' as const,
        severity: 'medium' as const,
        count: totalMissing,
        message: `${totalMissing} total translations missing`
      });
      recommendations.push('Use bulk translation tools to improve coverage');
    }

    // Calculate overall status
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (highPriorityMissing > 0) status = 'critical';
    else if (totalMissing > 10) status = 'warning';

    if (recommendations.length === 0) {
      recommendations.push('Translation system is healthy');
    }

    return {
      status,
      issues,
      recommendations
    };
  }
}

// Export singleton instance
export const translationStatusService = TranslationStatusService.getInstance();
