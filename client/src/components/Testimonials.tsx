import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import type { Country, Language } from "@/types";

type TestimonialsProps = {
  defaultState?: "TX" | "FL" | "NY";
  defaultCountry?: Country;
  defaultLanguage?: Language;
};

export default function Testimonials({ defaultState, defaultCountry, defaultLanguage }: TestimonialsProps) {
  const [activeState, setActiveState] = useState<string>(defaultState || "ALL");

  // Testimonial data
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      firm: "Davis Family Law, Austin TX",
      state: "TX",
      quote:
        "AiLex saves me at least 15 hours every week on legal research and drafting. The voice intake system has caught three potential clients I would have missed while in court.",
      hasVideo: true,
    },
    {
      id: 2,
      name: "<PERSON>",
      firm: "Rodriguez Immigration, Miami FL",
      state: "FL",
      quote:
        "Before AiLex, I was drowning in paperwork. Now I can focus on what matters—helping my clients. The research module alone is worth the subscription.",
      rating: 5,
    },
    {
      id: 3,
      name: "<PERSON>",
      firm: "Chen & Associates, NYC NY",
      state: "NY",
      quote:
        "I was skeptical about AI in legal practice until I tried AiLex. The New York-specific research capabilities are impressive and the drafting tools save me hours of work.",
      rating: 5,
    },
    {
      id: 4,
      name: "<PERSON>",
      firm: "Johnson Estate Planning, Houston TX",
      state: "TX",
      quote:
        "The document analysis feature has been game-changing for my estate planning practice. AiLex helps me review wills and trusts in a fraction of the time.",
      hasVideo: true,
    },
  ];

  // Filter testimonials by selected state
  const filteredTestimonials =
    activeState === "ALL"
      ? testimonials
      : testimonials.filter((t) => t.state === activeState);

  return (
    <section className="py-16 bg-[#F5F7FA]">
      <div className="container-content">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold mb-4 text-center">
            What Our Customers Say
          </h2>
          <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
            Hear from solo practitioners and small firms who have transformed
            their practice with AiLex.
          </p>
        </motion.div>

        <div className="flex flex-wrap justify-center gap-2 mb-8">
          <button
            onClick={() => setActiveState("ALL")}
            className={`px-3 py-1 rounded-full text-sm transition-default ${
              activeState === "ALL"
                ? "bg-primary text-white"
                : "bg-white hover:bg-primary hover:text-white"
            }`}
          >
            All
          </button>
          <button
            onClick={() => setActiveState("TX")}
            className={`px-3 py-1 rounded-full text-sm transition-default ${
              activeState === "TX"
                ? "bg-primary text-white"
                : "bg-white hover:bg-primary hover:text-white"
            }`}
          >
            TX
          </button>
          <button
            onClick={() => setActiveState("FL")}
            className={`px-3 py-1 rounded-full text-sm transition-default ${
              activeState === "FL"
                ? "bg-primary text-white"
                : "bg-white hover:bg-primary hover:text-white"
            }`}
          >
            FL
          </button>
          <button
            onClick={() => setActiveState("NY")}
            className={`px-3 py-1 rounded-full text-sm transition-default ${
              activeState === "NY"
                ? "bg-primary text-white"
                : "bg-white hover:bg-primary hover:text-white"
            }`}
          >
            NY
          </button>
        </div>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeState}
            className="grid md:grid-cols-2 gap-6"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            transition={{ duration: 0.3 }}
          >
            {filteredTestimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-default"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {testimonial.hasVideo && (
                  <div className="aspect-video bg-gray-100 relative flex items-center justify-center">
                    <div className="w-full h-full bg-gray-300"></div>
                    <div className="absolute inset-0 bg-navy bg-opacity-50 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-16 w-16 text-white opacity-80"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                )}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-bold text-lg">{testimonial.name}</h3>
                      <div className="text-sm text-gray-500">
                        {testimonial.firm}
                      </div>
                    </div>
                    <div className="code text-xs bg-primary bg-opacity-10 rounded-full px-2 py-1 text-primary">
                      {testimonial.state}
                    </div>
                  </div>
                  <p className="text-gray-600">{testimonial.quote}</p>

                  {testimonial.rating && (
                    <div className="mt-4 flex">
                      <div className="text-yellow-400 flex">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <svg
                            key={i}
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {filteredTestimonials.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              No testimonials available for the selected state.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
