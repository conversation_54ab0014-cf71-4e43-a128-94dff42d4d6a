import React, { useState, useEffect } from 'react';
import { useTranslation } from '@/contexts/TranslationContext';

// Component to test and display visibility system results
export default function VisibilityTest() {
  const { country, language, translationKeys, setCountry } = useTranslation();
  const [testResults, setTestResults] = useState<any>(null);

  // Test visibility for different countries
  const testVisibility = () => {
    const results = {
      US: {
        total: 0,
        shared: 0,
        usOnly: 0,
        belgiumOnly: 0,
        keys: [] as string[]
      },
      BE: {
        total: 0,
        shared: 0,
        usOnly: 0,
        belgiumOnly: 0,
        keys: [] as string[]
      }
    };

    // Test each key for each country
    translationKeys.forEach(key => {
      ['US', 'BE'].forEach(testCountry => {
        let visible = true;

        // Check hide rules
        if (key.hideInCountries?.includes(testCountry as any)) {
          visible = false;
        }

        // Check show rules
        if (visible && key.showInCountries?.length > 0) {
          visible = key.showInCountries.includes(testCountry as any);
        }

        // Check content type
        if (visible) {
          switch (key.contentType) {
            case 'us-only':
              visible = testCountry === 'US';
              break;
            case 'belgium-only':
              visible = testCountry === 'BE';
              break;
          }
        }

        if (visible) {
          const countryResults = results[testCountry as keyof typeof results];
          countryResults.total++;
          countryResults.keys.push(key.key);
          
          switch (key.contentType) {
            case 'us-only':
              countryResults.usOnly++;
              break;
            case 'belgium-only':
              countryResults.belgiumOnly++;
              break;
            case 'shared':
            default:
              countryResults.shared++;
              break;
          }
        }
      });
    });

    setTestResults(results);
  };

  useEffect(() => {
    if (translationKeys.length > 0) {
      testVisibility();
    }
  }, [translationKeys]);

  return (
    <div className="bg-white border rounded-lg p-6 shadow-sm">
      <h2 className="text-xl font-bold mb-4">🔍 Content Visibility System Test</h2>
      
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">
          Current Context: <strong>{country}</strong> ({language})
        </p>
        <p className="text-sm text-gray-600 mb-4">
          Total Keys Available: <strong>{translationKeys.length}</strong>
        </p>
      </div>

      {testResults && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* US Results */}
          <div className="border rounded-lg p-4 bg-blue-50">
            <h3 className="font-semibold text-blue-800 mb-3">🇺🇸 United States</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Visible Keys:</span>
                <span className="font-medium">{testResults.US.total}</span>
              </div>
              <div className="flex justify-between">
                <span>Shared Content:</span>
                <span className="font-medium">{testResults.US.shared}</span>
              </div>
              <div className="flex justify-between">
                <span>US-Only Content:</span>
                <span className="font-medium">{testResults.US.usOnly}</span>
              </div>
              <div className="flex justify-between">
                <span>Belgium-Only Content:</span>
                <span className="font-medium">{testResults.US.belgiumOnly}</span>
              </div>
            </div>
            
            <details className="mt-3">
              <summary className="text-xs text-blue-600 cursor-pointer">Show visible keys</summary>
              <div className="mt-2 text-xs bg-white p-2 rounded max-h-32 overflow-y-auto">
                {testResults.US.keys.map((key: string, index: number) => (
                  <div key={index} className="py-1 border-b border-gray-100 last:border-b-0">
                    {key}
                  </div>
                ))}
              </div>
            </details>
          </div>

          {/* Belgium Results */}
          <div className="border rounded-lg p-4 bg-yellow-50">
            <h3 className="font-semibold text-yellow-800 mb-3">🇧🇪 Belgium</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Visible Keys:</span>
                <span className="font-medium">{testResults.BE.total}</span>
              </div>
              <div className="flex justify-between">
                <span>Shared Content:</span>
                <span className="font-medium">{testResults.BE.shared}</span>
              </div>
              <div className="flex justify-between">
                <span>US-Only Content:</span>
                <span className="font-medium">{testResults.BE.usOnly}</span>
              </div>
              <div className="flex justify-between">
                <span>Belgium-Only Content:</span>
                <span className="font-medium">{testResults.BE.belgiumOnly}</span>
              </div>
            </div>
            
            <details className="mt-3">
              <summary className="text-xs text-yellow-600 cursor-pointer">Show visible keys</summary>
              <div className="mt-2 text-xs bg-white p-2 rounded max-h-32 overflow-y-auto">
                {testResults.BE.keys.map((key: string, index: number) => (
                  <div key={index} className="py-1 border-b border-gray-100 last:border-b-0">
                    {key}
                  </div>
                ))}
              </div>
            </details>
          </div>
        </div>
      )}

      {/* Test Controls */}
      <div className="mt-6 pt-4 border-t">
        <h4 className="font-medium mb-3">Test Controls</h4>
        <div className="flex gap-2">
          <button
            onClick={() => setCountry('US')}
            className={`px-3 py-1 rounded text-sm ${
              country === 'US' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Switch to US
          </button>
          <button
            onClick={() => setCountry('BE')}
            className={`px-3 py-1 rounded text-sm ${
              country === 'BE' 
                ? 'bg-yellow-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Switch to Belgium
          </button>
          <button
            onClick={testVisibility}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            Refresh Test
          </button>
        </div>
      </div>

      {/* Expected Results */}
      <div className="mt-4 p-3 bg-gray-50 rounded text-xs">
        <h5 className="font-medium mb-2">Expected Results:</h5>
        <ul className="space-y-1 text-gray-600">
          <li>• US should see: shared content + US-only content (no Belgium-only)</li>
          <li>• Belgium should see: shared content + Belgium-only content (no US-only)</li>
          <li>• Belgium-only content should only appear in Belgium</li>
          <li>• US-only content should only appear in US</li>
        </ul>
      </div>
    </div>
  );
}
