import React, { useState, useEffect } from 'react';
import { useAutoRedirect, autoRedirectUtils } from '@/hooks/useAutoRedirect';
import { useGeoLocation } from '@/hooks/useGeoLocation';

// Component to handle auto-redirection and show status
export default function AutoRedirectHandler() {
  const { isRedirecting, hasRedirected, optOutOfAutoRedirect, hasOptedOut } = useAutoRedirect();
  const { country, suggestedLanguage } = useGeoLocation();
  const [showOptOut, setShowOptOut] = useState(false);
  const [lastRedirect, setLastRedirect] = useState<any>(null);

  // Check if user was recently redirected
  useEffect(() => {
    const redirect = autoRedirectUtils.getLastRedirect();
    if (redirect && autoRedirectUtils.wasRedirectedRecently(2)) {
      setLastRedirect(redirect);
      setShowOptOut(true);
      
      // Auto-hide after 10 seconds
      setTimeout(() => setShowOptOut(false), 10000);
    }
  }, []);

  // Handle opt-out
  const handleOptOut = () => {
    optOutOfAutoRedirect();
    setShowOptOut(false);
    
    // Show confirmation
    alert('✅ Auto-redirect disabled. You can manually navigate to different country versions.');
  };

  // Show loading indicator during redirect
  if (isRedirecting) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm mx-4">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <div>
              <h3 className="font-medium text-gray-900">Redirecting...</h3>
              <p className="text-sm text-gray-600">
                Taking you to the {country === 'BE' ? 'Belgian' : 'US'} site
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show opt-out option if recently redirected
  if (showOptOut && lastRedirect && !hasOptedOut) {
    return (
      <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-40">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <span className="text-2xl">
              {lastRedirect.country === 'BE' ? '🇧🇪' : '🇺🇸'}
            </span>
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-gray-900 text-sm">
              Automatically redirected
            </h4>
            <p className="text-xs text-gray-600 mt-1">
              We detected you're in {lastRedirect.country === 'BE' ? 'Belgium' : 'the US'} and 
              redirected you to the appropriate site.
            </p>
            <div className="flex items-center space-x-2 mt-3">
              <button
                onClick={handleOptOut}
                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              >
                Disable auto-redirect
              </button>
              <button
                onClick={() => setShowOptOut(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Dismiss
              </button>
            </div>
          </div>
          <button
            onClick={() => setShowOptOut(false)}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  return null;
}

// Debug component for testing
export function AutoRedirectDebug() {
  const { isRedirecting, hasRedirected, hasOptedOut } = useAutoRedirect();
  const { country, suggestedLanguage, isLoading } = useGeoLocation();
  const lastRedirect = autoRedirectUtils.getLastRedirect();

  return (
    <div className="bg-gray-50 border rounded-lg p-4 text-xs">
      <h4 className="font-medium mb-2">🔄 Auto-Redirect Debug</h4>
      <div className="space-y-1">
        <div>Detection: {isLoading ? 'Loading...' : `${country || 'None'} (${suggestedLanguage})`}</div>
        <div>Is Redirecting: {isRedirecting ? 'Yes' : 'No'}</div>
        <div>Has Redirected: {hasRedirected ? 'Yes' : 'No'}</div>
        <div>Has Opted Out: {hasOptedOut ? 'Yes' : 'No'}</div>
        {lastRedirect && (
          <div>Last Redirect: {lastRedirect.from} → {lastRedirect.to}</div>
        )}
      </div>
      
      <div className="mt-3 space-x-2">
        <button
          onClick={() => autoRedirectUtils.clearRedirectHistory()}
          className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs"
        >
          Clear History
        </button>
      </div>
    </div>
  );
}
