import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useGeoLocation, geoLocationUtils } from '@/hooks/useGeoLocation';
import type { Country, Language } from '@/types';

// Props for the suggestion banner
interface LocationSuggestionBannerProps {
  className?: string;
}

// Country flag emojis
const countryFlags: Record<Country, string> = {
  'BE': '🇧🇪',
  'US': '🇺🇸'
};

// Language names
const languageNames: Record<Language, string> = {
  'en': 'English',
  'fr': 'French',
  'nl': 'Dutch'
};

export default function LocationSuggestionBanner({ className = '' }: LocationSuggestionBannerProps) {
  const [location, setLocation] = useLocation();
  const { country, suggestedLanguage, isLoading, storePreference } = useGeoLocation();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Check if we should show the suggestion
  useEffect(() => {
    if (isLoading || !country) {
      setIsVisible(false);
      return;
    }

    const shouldShow = geoLocationUtils.shouldShowSuggestion(
      location,
      country,
      suggestedLanguage
    );

    if (shouldShow) {
      // Small delay for better UX
      setTimeout(() => {
        setIsVisible(true);
        setIsAnimating(true);
      }, 1000);
    }
  }, [location, country, suggestedLanguage, isLoading]);

  // Handle accepting the suggestion
  const handleAccept = () => {
    if (!country) return;
    
    const suggestedPath = geoLocationUtils.getSuggestedPath(country, suggestedLanguage);
    
    // Store preference as accepted
    storePreference(country, suggestedLanguage, false);
    
    // Navigate to suggested path
    setLocation(suggestedPath);
    
    // Hide banner
    hideBanner();
  };

  // Handle dismissing the suggestion
  const handleDismiss = () => {
    if (!country) return;
    
    // Store preference as dismissed
    storePreference(country, suggestedLanguage, true);
    
    // Hide banner
    hideBanner();
  };

  // Hide banner with animation
  const hideBanner = () => {
    setIsAnimating(false);
    setTimeout(() => setIsVisible(false), 300);
  };

  // Don't render if not visible
  if (!isVisible || !country) {
    return null;
  }

  const flag = countryFlags[country];
  const languageName = languageNames[suggestedLanguage];
  const suggestionText = geoLocationUtils.getSuggestionText(country, suggestedLanguage);
  const suggestedPath = geoLocationUtils.getSuggestedPath(country, suggestedLanguage);

  return (
    <div 
      className={`
        fixed top-0 left-0 right-0 z-50 
        bg-gradient-to-r from-blue-50 to-indigo-50 
        border-b border-blue-200 
        shadow-sm
        transition-all duration-300 ease-in-out
        ${isAnimating ? 'transform translate-y-0 opacity-100' : 'transform -translate-y-full opacity-0'}
        ${className}
      `}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          {/* Suggestion text */}
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{flag}</span>
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
              <span className="text-sm font-medium text-blue-900">
                {suggestionText}
              </span>
              {suggestedLanguage !== 'en' && (
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                  {languageName}
                </span>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleAccept}
              className="
                inline-flex items-center px-4 py-2 
                bg-blue-600 hover:bg-blue-700 
                text-white text-sm font-medium 
                rounded-md transition-colors duration-200
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              "
            >
              {country === 'BE' ? (
                <>
                  <span className="mr-1">🇧🇪</span>
                  Switch to Belgian site
                </>
              ) : (
                <>
                  <span className="mr-1">🇺🇸</span>
                  Switch to US site
                </>
              )}
            </button>
            
            <button
              onClick={handleDismiss}
              className="
                inline-flex items-center px-3 py-2 
                text-blue-600 hover:text-blue-800 
                text-sm font-medium 
                transition-colors duration-200
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md
              "
            >
              No, stay here
            </button>
            
            <button
              onClick={handleDismiss}
              className="
                p-1 text-blue-400 hover:text-blue-600 
                transition-colors duration-200
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md
              "
              aria-label="Close suggestion"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Loading indicator (if needed) */}
      {isLoading && (
        <div className="absolute inset-0 bg-blue-50 bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm">Detecting location...</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Hook to get banner height for layout adjustments
export function useLocationBannerHeight() {
  const { country, isLoading } = useGeoLocation();
  const [location] = useLocation();
  
  const shouldShow = !isLoading && country && geoLocationUtils.shouldShowSuggestion(
    location,
    country,
    'en' // Default for height calculation
  );
  
  return shouldShow ? 60 : 0; // Approximate banner height
}

// Utility component to add top padding when banner is shown
export function LocationBannerSpacer({ children }: { children: React.ReactNode }) {
  const bannerHeight = useLocationBannerHeight();
  
  return (
    <div style={{ paddingTop: bannerHeight }}>
      {children}
    </div>
  );
}
