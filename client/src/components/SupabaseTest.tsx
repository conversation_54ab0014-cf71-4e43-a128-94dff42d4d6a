import React, { useState, useEffect } from 'react';
import { TranslationService } from '@/services/translationService';
import type { Language } from '@/types';

// Test component to verify Supabase integration
export default function SupabaseTest() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testSupabaseConnection = async () => {
    setStatus('loading');
    setTestResults([]);
    addTestResult('🧪 Starting Supabase integration tests...');

    try {
      // Test 1: Fetch categories
      addTestResult('📂 Testing categories fetch...');
      const categoriesResult = await TranslationService.getCategories();
      addTestResult(`✅ Categories: Found ${categoriesResult.categories.length} categories`);

      // Test 2: Fetch translation keys
      addTestResult('🔑 Testing translation keys fetch...');
      const keysResult = await TranslationService.getTranslationKeys();
      addTestResult(`✅ Translation Keys: Found ${keysResult.keys.length} keys`);

      // Test 3: Fetch translations
      addTestResult('🌐 Testing translations fetch...');
      const translationsResult = await TranslationService.getTranslations();
      addTestResult(`✅ Translations: Found ${translationsResult.translations.length} translations`);

      // Test 4: Test with filters
      addTestResult('🇧🇪 Testing Belgian content filter...');
      const belgianKeysResult = await TranslationService.getTranslationKeys({ country: 'BE' });
      addTestResult(`✅ Belgian Keys: Found ${belgianKeysResult.keys.length} keys`);

      // Test 5: Test French translations
      addTestResult('🇫🇷 Testing French translations...');
      const frenchTranslationsResult = await TranslationService.getTranslations({ language: 'fr' });
      addTestResult(`✅ French Translations: Found ${frenchTranslationsResult.translations.length} translations`);

      setData({
        categories: categoriesResult.categories,
        keys: keysResult.keys,
        translations: translationsResult.translations,
        belgianKeys: belgianKeysResult.keys,
        frenchTranslations: frenchTranslationsResult.translations,
      });

      setStatus('success');
      addTestResult('🎉 All tests passed! Supabase integration is working correctly.');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setStatus('error');
      addTestResult(`❌ Test failed: ${errorMessage}`);
    }
  };

  const testCreateTranslationKey = async () => {
    try {
      addTestResult('🆕 Testing translation key creation...');
      
      const testKey = {
        key: `test.key.${Date.now()}`,
        category: 'general',
        description: 'Test key created by Supabase integration test',
        defaultContent: 'This is a test translation key',
        showInCountries: ['US', 'BE'] as any,
        contentType: 'shared',
      };

      const result = await TranslationService.createTranslationKey(testKey);
      addTestResult(`✅ Created test key: ${result.key.key}`);

      // Refresh the data
      await testSupabaseConnection();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      addTestResult(`❌ Failed to create test key: ${errorMessage}`);
    }
  };

  useEffect(() => {
    testSupabaseConnection();
  }, []);

  return (
    <div className="bg-white border rounded-lg p-6 shadow-sm">
      <h2 className="text-xl font-bold mb-4">🗄️ Supabase Integration Test</h2>
      
      {/* Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-2">
          <span className="font-medium">Status:</span>
          <span className={`px-2 py-1 rounded text-sm ${
            status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
            status === 'success' ? 'bg-green-100 text-green-800' :
            'bg-red-100 text-red-800'
          }`}>
            {status === 'loading' ? '⏳ Testing...' :
             status === 'success' ? '✅ Success' :
             '❌ Error'}
          </span>
        </div>
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            {error}
          </div>
        )}
      </div>

      {/* Test Results */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Test Results:</h3>
        <div className="bg-gray-50 rounded p-3 max-h-40 overflow-y-auto">
          {testResults.map((result, index) => (
            <div key={index} className="text-sm font-mono mb-1">
              {result}
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2 mb-4">
        <button
          onClick={testSupabaseConnection}
          disabled={status === 'loading'}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          🔄 Retest Connection
        </button>
        <button
          onClick={testCreateTranslationKey}
          disabled={status === 'loading'}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          🆕 Test Create Key
        </button>
      </div>

      {/* Data Summary */}
      {data && status === 'success' && (
        <div className="border-t pt-4">
          <h3 className="font-medium mb-2">Data Summary:</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Categories:</strong> {data.categories.length}
              <ul className="ml-4 mt-1">
                {data.categories.slice(0, 3).map((cat: any) => (
                  <li key={cat.id}>• {cat.name}</li>
                ))}
                {data.categories.length > 3 && <li>• ... and {data.categories.length - 3} more</li>}
              </ul>
            </div>
            <div>
              <strong>Translation Keys:</strong> {data.keys.length}
              <ul className="ml-4 mt-1">
                {data.keys.slice(0, 3).map((key: any) => (
                  <li key={key.id}>• {key.key}</li>
                ))}
                {data.keys.length > 3 && <li>• ... and {data.keys.length - 3} more</li>}
              </ul>
            </div>
            <div>
              <strong>Translations:</strong> {data.translations.length}
              <ul className="ml-4 mt-1">
                {data.translations.slice(0, 2).map((trans: any) => (
                  <li key={trans.id}>• {trans.language}: {trans.content.substring(0, 30)}...</li>
                ))}
                {data.translations.length > 2 && <li>• ... and {data.translations.length - 2} more</li>}
              </ul>
            </div>
            <div>
              <strong>Belgian Keys:</strong> {data.belgianKeys.length}
              <strong className="block mt-2">French Translations:</strong> {data.frenchTranslations.length}
            </div>
          </div>
        </div>
      )}

      {/* Environment Info */}
      <div className="border-t pt-4 mt-4 text-xs text-gray-500">
        <p><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not configured'}</p>
        <p><strong>Anon Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Configured' : 'Not configured'}</p>
      </div>
    </div>
  );
}
