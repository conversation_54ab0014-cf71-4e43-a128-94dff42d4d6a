import React from 'react';
import { useTranslation } from '@/contexts/TranslationContext';
import type { TranslationOptions } from '@/contexts/TranslationContext';
import type { TranslationKey } from '@/types/translations';

// Props for TranslatedText component
interface TranslatedTextProps {
  // Translation key (type-safe)
  tKey: TranslationKey;
  
  // Translation options
  options?: TranslationOptions;
  
  // HTML element to render
  as?: keyof JSX.IntrinsicElements;
  
  // Additional props for the element
  className?: string;
  style?: React.CSSProperties;
  
  // Children (for complex content)
  children?: React.ReactNode;
  
  // Fallback content if translation not found
  fallback?: string;
  
  // Whether to apply country-specific styling
  useCountryStyles?: boolean;
}

// TranslatedText component for simple text rendering
export function TranslatedText({
  tKey,
  options = {},
  as: Element = 'span',
  className = '',
  style,
  children,
  fallback,
  useCountryStyles = false,
  ...props
}: TranslatedTextProps & React.HTMLAttributes<HTMLElement>) {
  const { tWithMeta } = useTranslation();
  
  const result = tWithMeta(tKey, { ...options, fallback });
  
  // Don't render if not visible
  if (!result.isVisible) {
    return null;
  }
  
  // Combine classNames
  let finalClassName = className;
  if (useCountryStyles && result.className) {
    finalClassName = `${className} ${result.className}`.trim();
  }
  
  return React.createElement(
    Element,
    {
      className: finalClassName,
      style,
      ...props
    },
    children || result.content
  );
}

// Conditional wrapper component
interface ConditionalTranslationProps {
  tKey: TranslationKey;
  options?: TranslationOptions;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ConditionalTranslation({
  tKey,
  options = {},
  children,
  fallback = null
}: ConditionalTranslationProps) {
  const { tShow } = useTranslation();
  
  const isVisible = tShow(tKey, options);
  
  if (!isVisible) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

// Hook for conditional rendering
export function useConditionalRender() {
  const { tShow } = useTranslation();
  
  return {
    renderIf: (tKey: TranslationKey, options?: TranslationOptions) => (component: React.ReactNode) => {
      return tShow(tKey, options) ? component : null;
    },
    showIf: (tKey: TranslationKey, options?: TranslationOptions) => {
      return tShow(tKey, options);
    }
  };
}

// Pre-configured components for common use cases
export const TranslatedHeading = (props: Omit<TranslatedTextProps, 'as'>) => (
  <TranslatedText {...props} as="h1" useCountryStyles />
);

export const TranslatedSubheading = (props: Omit<TranslatedTextProps, 'as'>) => (
  <TranslatedText {...props} as="h2" useCountryStyles />
);

export const TranslatedParagraph = (props: Omit<TranslatedTextProps, 'as'>) => (
  <TranslatedText {...props} as="p" />
);

export const TranslatedButton = (props: Omit<TranslatedTextProps, 'as'>) => (
  <TranslatedText {...props} as="button" useCountryStyles />
);

export const TranslatedSpan = (props: Omit<TranslatedTextProps, 'as'>) => (
  <TranslatedText {...props} as="span" />
);

// Example usage components
export function ExampleUsage() {
  const { t, tShow } = useTranslation();
  const { renderIf } = useConditionalRender();
  
  return (
    <div>
      {/* Simple text translation */}
      <TranslatedHeading tKey="hero.title" className="text-4xl font-bold" />
      
      {/* With variables */}
      <TranslatedText 
        tKey="welcome.message" 
        options={{ variables: { name: 'John' } }}
      />
      
      {/* Conditional rendering */}
      <ConditionalTranslation tKey="legal.belgian_certification">
        <div className="bg-blue-100 p-4">
          <TranslatedText tKey="legal.belgian_certification" />
        </div>
      </ConditionalTranslation>
      
      {/* Using hook for conditional rendering */}
      {renderIf('features.belgium_specific')(
        <div>Belgium-specific features here</div>
      )}
      
      {/* Traditional approach */}
      <h1>{t('hero.title')}</h1>
      {tShow('legal.belgian_certification') && (
        <p>{t('legal.belgian_certification')}</p>
      )}
    </div>
  );
}

export default TranslatedText;
