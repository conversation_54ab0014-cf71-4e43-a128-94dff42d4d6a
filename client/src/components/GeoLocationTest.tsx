import React, { useState } from 'react';
import { useGeoLocation } from '@/hooks/useGeoLocation';
import type { Country, Language } from '@/types';

// Test component for geolocation functionality
export default function GeoLocationTest() {
  const { country, suggestedLanguage, isLoading, error, storePreference, getStoredPreference } = useGeoLocation();
  const [testMode, setTestMode] = useState(false);
  const [simulatedCountry, setSimulatedCountry] = useState<Country>('BE');
  const [simulatedLanguage, setSimulatedLanguage] = useState<Language>('fr');

  // Get stored preference
  const storedPreference = getStoredPreference();

  // Clear stored preferences
  const clearPreferences = () => {
    localStorage.removeItem('ailex-location-preference');
    localStorage.removeItem('ailex-dev-location-override');
    window.location.reload(); // Reload to reset state
  };

  // Set dev override to force banner on main site
  const setDevOverride = (country: Country, language: Language) => {
    const override = { country, language };
    localStorage.setItem('ailex-dev-location-override', JSON.stringify(override));
    alert(`🧪 DEV OVERRIDE SET: ${country} + ${language}\n\nNow go to the main site (/) to see the banner!`);
    window.location.reload(); // Reload to apply override
  };

  // Simulate different locations for testing
  const simulateLocation = (country: Country, language: Language) => {
    // Store a test preference
    storePreference(country, language, false);
    
    // Show alert for testing
    alert(`Simulated: ${country} with ${language} language preference stored!`);
  };

  return (
    <div className="bg-white border rounded-lg p-6 shadow-sm">
      <h2 className="text-xl font-bold mb-4">🌍 Geolocation Detection Test</h2>
      
      {/* Current Detection Results */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3">Current Detection Results:</h3>
        <div className="bg-gray-50 p-4 rounded-lg space-y-2">
          <div className="flex justify-between">
            <span className="font-medium">Status:</span>
            <span className={isLoading ? 'text-yellow-600' : 'text-green-600'}>
              {isLoading ? 'Loading...' : 'Complete'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Detected Country:</span>
            <span className="font-mono">
              {country ? `${country} ${country === 'BE' ? '🇧🇪' : '🇺🇸'}` : 'Not detected / Other'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Browser Language:</span>
            <span className="font-mono">{suggestedLanguage}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Navigator Language:</span>
            <span className="font-mono text-xs">{navigator.language}</span>
          </div>
          {error && (
            <div className="flex justify-between">
              <span className="font-medium text-red-600">Error:</span>
              <span className="text-red-600 text-sm">{error}</span>
            </div>
          )}
        </div>
      </div>

      {/* Stored Preferences */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3">Stored Preferences:</h3>
        <div className="bg-blue-50 p-4 rounded-lg">
          {storedPreference ? (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Chosen Country:</span>
                <span>{storedPreference.chosenCountry}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Chosen Language:</span>
                <span>{storedPreference.chosenLanguage}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Dismissed:</span>
                <span>{storedPreference.dismissed ? 'Yes' : 'No'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Stored:</span>
                <span className="text-xs">
                  {new Date(storedPreference.timestamp).toLocaleString()}
                </span>
              </div>
            </div>
          ) : (
            <p className="text-gray-600">No preferences stored</p>
          )}
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3">Test Controls:</h3>
        <div className="space-y-4">
          {/* Clear preferences */}
          <button
            onClick={clearPreferences}
            className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Clear All Preferences & Reload
          </button>

          {/* Dev Override for Banner Testing */}
          <div className="border-2 border-purple-200 rounded-lg p-4 bg-purple-50">
            <h4 className="font-medium mb-3 text-purple-800">🧪 DEV: Force Banner on Main Site</h4>
            <p className="text-sm text-purple-600 mb-3">
              This will make the banner appear on the main website (/) even if you're not in Belgium:
            </p>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => setDevOverride('BE', 'fr')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                🇧🇪 Belgium + French
              </button>
              <button
                onClick={() => setDevOverride('BE', 'nl')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                🇧🇪 Belgium + Dutch
              </button>
              <button
                onClick={() => setDevOverride('BE', 'en')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                🇧🇪 Belgium + English
              </button>
              <button
                onClick={() => setDevOverride('US', 'en')}
                className="px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                🇺🇸 US (no banner)
              </button>
            </div>
          </div>

          {/* Simulation controls */}
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-3">Simulate Location Preference:</h4>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Country:</label>
                <select
                  value={simulatedCountry}
                  onChange={(e) => setSimulatedCountry(e.target.value as Country)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="US">🇺🇸 United States</option>
                  <option value="BE">🇧🇪 Belgium</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Language:</label>
                <select
                  value={simulatedLanguage}
                  onChange={(e) => setSimulatedLanguage(e.target.value as Language)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="en">English</option>
                  <option value="fr">French</option>
                  <option value="nl">Dutch</option>
                </select>
              </div>
            </div>
            <button
              onClick={() => simulateLocation(simulatedCountry, simulatedLanguage)}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Simulate {simulatedCountry} with {simulatedLanguage}
            </button>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 mb-2">Testing Instructions:</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• The banner should appear if you're detected as being in Belgium</li>
          <li>• Use "Clear Preferences" to reset and see the banner again</li>
          <li>• The banner won't show on admin pages or if already on suggested page</li>
          <li>• Preferences are stored for 30 days</li>
          <li>• Use browser dev tools to change navigator.language for language testing</li>
        </ul>
      </div>

      {/* Current URL Info */}
      <div className="mt-4 text-xs text-gray-500">
        <p>Current URL: {window.location.pathname}</p>
        <p>User Agent: {navigator.userAgent.substring(0, 100)}...</p>
      </div>
    </div>
  );
}
