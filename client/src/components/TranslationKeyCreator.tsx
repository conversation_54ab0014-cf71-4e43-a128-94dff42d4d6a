/**
 * Translation Key Creator Component
 * 
 * Provides a UI for developers to create new translation keys
 * with validation, suggestions, and real-time feedback.
 */

import React, { useState, useEffect } from 'react';
import { useTranslationFactory, useTranslationKeyValidation } from '@/hooks/useTranslationFactory';
import type { TranslationCategory, ContentType } from '@/types/translations';

interface TranslationKeyCreatorProps {
  onKeyCreated?: (key: string) => void;
  onClose?: () => void;
  defaultCategory?: TranslationCategory;
  defaultContentType?: ContentType;
}

export default function TranslationKeyCreator({
  onKeyCreated,
  onClose,
  defaultCategory,
  defaultContentType = 'shared'
}: TranslationKeyCreatorProps) {
  const [key, setKey] = useState('');
  const [content, setContent] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState<TranslationCategory>(defaultCategory || 'general');
  const [contentType, setContentType] = useState<ContentType>(defaultContentType);
  
  const { createKey, isCreating, lastResult, clearResult } = useTranslationFactory();
  const { validate, isValid, errors, warnings, suggestions } = useTranslationKeyValidation();

  // Real-time validation
  useEffect(() => {
    if (key || content) {
      validate(key, content);
    }
  }, [key, content, validate]);

  // Apply suggestions
  useEffect(() => {
    if (suggestions.category && !defaultCategory) {
      setCategory(suggestions.category);
    }
    if (suggestions.description && !description) {
      setDescription(suggestions.description);
    }
  }, [suggestions, defaultCategory, description]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isValid) {
      return;
    }

    const result = await createKey({
      key,
      defaultContent: content,
      description: description || suggestions.description,
      category,
      contentType,
      showInCountries: contentType === 'belgium-only' ? ['BE'] : 
                      contentType === 'us-only' ? ['US'] : ['US', 'BE']
    });

    if (result.success) {
      onKeyCreated?.(key);
      // Reset form
      setKey('');
      setContent('');
      setDescription('');
      setCategory(defaultCategory || 'general');
      setContentType(defaultContentType);
    }
  };

  const handleClose = () => {
    clearResult();
    onClose?.();
  };

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Create Translation Key</h3>
        {onClose && (
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Key Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Translation Key *
          </label>
          <input
            type="text"
            value={key}
            onChange={(e) => setKey(e.target.value)}
            placeholder="e.g., hero.new_title"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              key && !isValid ? 'border-red-300' : 'border-gray-300'
            }`}
            required
          />
          {suggestions.key && (
            <p className="text-sm text-blue-600 mt-1">
              💡 Suggested format: {suggestions.key}
            </p>
          )}
        </div>

        {/* Content Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Default Content (English) *
          </label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Enter the English text content..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Category Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value as TranslationCategory)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="general">General</option>
            <option value="hero">Hero</option>
            <option value="features">Features</option>
            <option value="problems">Problems</option>
            <option value="solutions">Solutions</option>
            <option value="legal">Legal</option>
            <option value="seo">SEO</option>
          </select>
          {suggestions.category && suggestions.category !== category && (
            <p className="text-sm text-blue-600 mt-1">
              💡 Suggested category: {suggestions.category}
            </p>
          )}
        </div>

        {/* Content Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Content Type
          </label>
          <select
            value={contentType}
            onChange={(e) => setContentType(e.target.value as ContentType)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="shared">Shared (US + Belgium)</option>
            <option value="us-only">US Only</option>
            <option value="belgium-only">Belgium Only</option>
          </select>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description (Optional)
          </label>
          <input
            type="text"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder={suggestions.description || "Brief description of this content"}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Validation Messages */}
        {errors.length > 0 && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <h4 className="text-sm font-medium text-red-800 mb-1">Errors:</h4>
            <ul className="text-sm text-red-700 list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {warnings.length > 0 && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <h4 className="text-sm font-medium text-yellow-800 mb-1">Warnings:</h4>
            <ul className="text-sm text-yellow-700 list-disc list-inside">
              {warnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Result Messages */}
        {lastResult && (
          <div className={`p-3 border rounded-md ${
            lastResult.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <p className={`text-sm font-medium ${
              lastResult.success ? 'text-green-800' : 'text-red-800'
            }`}>
              {lastResult.success ? '✅ Success!' : '❌ Error'}
            </p>
            <p className={`text-sm ${
              lastResult.success ? 'text-green-700' : 'text-red-700'
            }`}>
              {lastResult.success 
                ? `Translation key '${lastResult.key}' created successfully!`
                : lastResult.error
              }
            </p>
            {lastResult.typesRegenerated && (
              <p className="text-sm text-green-600 mt-1">
                🔄 TypeScript types updated automatically
              </p>
            )}
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          {onClose && (
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={!isValid || isCreating}
            className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              !isValid || isCreating
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isCreating ? 'Creating...' : 'Create Translation Key'}
          </button>
        </div>
      </form>
    </div>
  );
}
