/**
 * TypeScript types for translation keys
 * Auto-generated from database translation keys
 * 
 * This file provides compile-time validation for translation key usage
 * and prevents typos in t() function calls.
 * 
 * Last updated: 2025-07-25
 * Total keys: 39
 * 
 * To regenerate: npm run generate-types
 */

/**
 * All available translation keys in the system
 * Use these with the t() function: t('hero.title_main')
 */
export type TranslationKey = 
  | 'features.drafting.description'
  | 'features.drafting.title'
  | 'features.intake.description'
  | 'features.intake.title'
  | 'features.research.description'
  | 'features.research.title'
  | 'features.title'
  | 'hero.cta_primary'
  | 'hero.cta_primary_trial'
  | 'hero.cta_secondary'
  | 'hero.cta_secondary_demo'
  | 'hero.subtitle'
  | 'hero.subtitle_belgian'
  | 'hero.subtitle_main'
  | 'hero.tagline'
  | 'hero.title'
  | 'hero.title_belgian'
  | 'hero.title_main'
  | 'legal.belgian_certification'
  | 'legal.us_bar_admission'
  | 'problems.admin_time.description'
  | 'problems.admin_time.title'
  | 'problems.expensive_software.description'
  | 'problems.expensive_software.title'
  | 'problems.missed_calls.description'
  | 'problems.missed_calls.title'
  | 'seo.description_belgian'
  | 'seo.title_belgian'
  | 'solutions.case_management.description'
  | 'solutions.case_management.title'
  | 'solutions.document_drafting.description'
  | 'solutions.document_drafting.title'
  | 'solutions.pricing.description'
  | 'solutions.pricing.title'
  | 'solutions.receptionist.description'
  | 'solutions.receptionist.title'
  | 'solutions.time_saved.case_management'
  | 'solutions.time_saved.receptionist'
  | 'welcome.message';

/**
 * Translation key categories for better organization
 */
export type TranslationCategory = 
  | 'features'
  | 'hero'
  | 'legal'
  | 'problems'
  | 'seo'
  | 'solutions'
  | 'welcome';

/**
 * Helper type to get keys by category
 */
export type KeysByCategory<T extends TranslationCategory> = 
  T extends 'features' ? Extract<TranslationKey, `features.${string}`> :
  T extends 'hero' ? Extract<TranslationKey, `hero.${string}`> :
  T extends 'legal' ? Extract<TranslationKey, `legal.${string}`> :
  T extends 'problems' ? Extract<TranslationKey, `problems.${string}`> :
  T extends 'seo' ? Extract<TranslationKey, `seo.${string}`> :
  T extends 'solutions' ? Extract<TranslationKey, `solutions.${string}`> :
  T extends 'welcome' ? Extract<TranslationKey, `welcome.${string}`> :
  never;

/**
 * Content type classification
 */
export type ContentType = 'shared' | 'us-only' | 'belgium-only';

/**
 * Supported languages
 */
export type Language = 'en' | 'fr' | 'nl';

/**
 * Countries where content is shown
 */
export type Country = 'US' | 'BE';

/**
 * Translation function type with strict key validation
 */
export type TranslationFunction = (key: TranslationKey) => string;

/**
 * Translation hook return type
 */
export interface UseTranslationReturn {
  t: TranslationFunction;
  language: Language;
  country: Country;
  isLoading: boolean;
}

/**
 * Utility type to validate translation key at compile time
 * Usage: const key: ValidTranslationKey<'hero.title'> = 'hero.title';
 */
export type ValidTranslationKey<T extends string> = T extends TranslationKey ? T : never;

/**
 * Category-specific key types for better organization
 */
export type FeaturesKeys = 
  | 'features.drafting.description'
  | 'features.drafting.title'
  | 'features.intake.description'
  | 'features.intake.title'
  | 'features.research.description'
  | 'features.research.title'
  | 'features.title';

export type HeroKeys = 
  | 'hero.cta_primary'
  | 'hero.cta_primary_trial'
  | 'hero.cta_secondary'
  | 'hero.cta_secondary_demo'
  | 'hero.subtitle'
  | 'hero.subtitle_belgian'
  | 'hero.subtitle_main'
  | 'hero.tagline'
  | 'hero.title'
  | 'hero.title_belgian'
  | 'hero.title_main';

export type LegalKeys = 
  | 'legal.belgian_certification'
  | 'legal.us_bar_admission';

export type ProblemsKeys = 
  | 'problems.admin_time.description'
  | 'problems.admin_time.title'
  | 'problems.expensive_software.description'
  | 'problems.expensive_software.title'
  | 'problems.missed_calls.description'
  | 'problems.missed_calls.title';

export type SeoKeys = 
  | 'seo.description_belgian'
  | 'seo.title_belgian';

export type SolutionsKeys = 
  | 'solutions.case_management.description'
  | 'solutions.case_management.title'
  | 'solutions.document_drafting.description'
  | 'solutions.document_drafting.title'
  | 'solutions.pricing.description'
  | 'solutions.pricing.title'
  | 'solutions.receptionist.description'
  | 'solutions.receptionist.title'
  | 'solutions.time_saved.case_management'
  | 'solutions.time_saved.receptionist';

export type WelcomeKeys = 
  | 'welcome.message';
