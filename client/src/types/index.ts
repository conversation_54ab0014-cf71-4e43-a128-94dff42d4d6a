// Location types for US states and countries
export type State = "TX" | "FL" | "NY";
export type Country = "US" | "BE";
export type Location = State | Country;

// Language types for internationalization
export type Language = "en" | "fr" | "nl";

// Testimonial type
export interface Testimonial {
  id: number;
  name: string;
  firm: string;
  location: Location; // Changed from 'state' to 'location' to support countries
  quote: string;
  rating?: number;
  hasVideo?: boolean;
  language?: Language; // Add language support for testimonials
}

// Pricing plan type
export interface PricingPlan {
  name: string;
  subtitle: string;
  price: number;
  features: string[];
  disabledFeatures: string[];
  popular: boolean;
  cta: string;
}

// FAQ type
export interface FAQ {
  question: string;
  answer: string;
}

// Security feature type
export interface SecurityFeature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Feature card type
export interface FeatureCard {
  id: number;
  code: string;
  title: string;
  description: string;
  demoTitle: string;
  demoContent: React.ReactNode;
}

// Problem card type
export interface ProblemCard {
  emoji: string;
  title: string;
  description: string;
}

// Logo type
export interface Logo {
  name: string;
  altText: string;
}

// Navigation item type
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

// Form submission type
export interface ContactSubmission {
  email: string;
  name?: string;
  message?: string;
}

// Prospect signup types
export interface ProspectSignupRequest {
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  signupSource:
    | "website"
    | "landing_page"
    | "referral"
    | "social_media"
    | "advertisement"
    | "other";
  signupPage?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmContent?: string;
  utmTerm?: string;
  practiceAreaInterest?: (
    | "personal_injury"
    | "criminal_defense"
    | "family_law"
  )[];
  caseUrgency?:
    | "immediate"
    | "within_month"
    | "within_quarter"
    | "planning_ahead";
  estimatedCaseValue?:
    | "under_10k"
    | "10k_50k"
    | "50k_100k"
    | "over_100k"
    | "unknown";
  newsletterSubscribed?: boolean;
  marketingConsent?: boolean;
  communicationPreferences?: {
    email: boolean;
    sms: boolean;
    phone: boolean;
  };
  gdprConsent: boolean;
  turnstileToken?: string;
}

export interface ProspectSignupResponse {
  success: boolean;
  message: string;
  prospectId?: string;
  emailVerified?: boolean;
  error?: string;
}

export interface EmailVerificationRequest {
  token: string;
}

export interface UTMParameters {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
}

// Translation management types (legacy - kept for compatibility)
export interface Translation {
  id: string;
  key: string;
  language: Language;
  content: string;
  context?: string; // Additional context for translators
  createdAt: Date;
  updatedAt: Date;
}

export interface TranslationKey {
  id: string;
  key: string;
  description?: string;
  category?: string; // e.g., 'hero', 'pricing', 'features'
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced translation types for new system
export interface EnhancedTranslationKey {
  id: string;
  key: string;
  category: string;
  description: string;
  defaultContent: string; // US English source of truth
  showInCountries: Country[];
  hideInCountries: Country[];
  contentType: 'shared' | 'country-specific' | 'belgium-only' | 'us-only';
  metadata?: {
    belgiumVariant?: string;
    legalContext?: boolean;
    priority?: number;
    styling?: {
      belgiumClassName?: string;
      usClassName?: string;
    };
  };
  created_at: string;
  updated_at: string;
}

export interface EnhancedTranslation {
  id: string;
  keyId: string;
  language: Language;
  content: string;
  context?: string;
  isAutoTranslated: boolean;
  confidence?: number;
  created_at: string;
  updated_at: string;
}

// Gemini translation request/response types
export interface GeminiTranslationRequest {
  text: string;
  targetLanguage: Language;
  context?: string;
  legalContext?: boolean;
}

export interface GeminiTranslationResponse {
  translatedText: string;
  confidence?: number;
  suggestions?: string[];
}
