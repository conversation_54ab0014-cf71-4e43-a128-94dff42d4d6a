// Database types generated from Supabase schema
export interface Database {
  public: {
    Tables: {
      translation_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          display_order: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          display_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          display_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      translation_keys: {
        Row: {
          id: string;
          key: string;
          category_id: string | null;
          description: string | null;
          default_content: string;
          show_in_countries: string[];
          hide_in_countries: string[];
          content_type: string;
          metadata: any;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          key: string;
          category_id?: string | null;
          description?: string | null;
          default_content: string;
          show_in_countries?: string[];
          hide_in_countries?: string[];
          content_type?: string;
          metadata?: any;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          key?: string;
          category_id?: string | null;
          description?: string | null;
          default_content?: string;
          show_in_countries?: string[];
          hide_in_countries?: string[];
          content_type?: string;
          metadata?: any;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      translations: {
        Row: {
          id: string;
          key_id: string;
          language: string;
          content: string;
          context: string | null;
          is_auto_translated: boolean;
          confidence: number | null;
          is_approved: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          key_id: string;
          language: string;
          content: string;
          context?: string | null;
          is_auto_translated?: boolean;
          confidence?: number | null;
          is_approved?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          key_id?: string;
          language?: string;
          content?: string;
          context?: string | null;
          is_auto_translated?: boolean;
          confidence?: number | null;
          is_approved?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      translation_audit_log: {
        Row: {
          id: string;
          table_name: string;
          record_id: string;
          action: string;
          old_values: any | null;
          new_values: any | null;
          user_id: string | null;
          user_email: string | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          table_name: string;
          record_id: string;
          action: string;
          old_values?: any | null;
          new_values?: any | null;
          user_id?: string | null;
          user_email?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          table_name?: string;
          record_id?: string;
          action?: string;
          old_values?: any | null;
          new_values?: any | null;
          user_id?: string | null;
          user_email?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
