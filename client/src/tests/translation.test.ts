/**
 * Translation System Test Suite
 *
 * Basic tests for the translation system functionality
 */

import { describe, it, expect, vi } from 'vitest';
import { translationKeyFactory } from '@/lib/translationKeyFactory';
import type { TranslationKey } from '@/types/translations';

describe('Translation System', () => {

  describe('Translation Key Validation', () => {
    it('should validate correct key format', () => {
      const validation = translationKeyFactory.validateKey('hero.title_main', 'Test content');

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid key formats', () => {
      const validation = translationKeyFactory.validateKey('Invalid-Key!', 'Test content');

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain('lowercase letters');
    });

    it('should reject empty keys', () => {
      const validation = translationKeyFactory.validateKey('', 'Test content');

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Key cannot be empty');
    });

    it('should reject empty content', () => {
      const validation = translationKeyFactory.validateKey('test.key', '');

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Default content cannot be empty');
    });

    it('should suggest category based on key', () => {
      const validation = translationKeyFactory.validateKey('hero.new_title', 'Test content');

      expect(validation.suggestions.category).toBe('hero');
    });
  });

  describe('Type Safety', () => {
    it('should only accept valid translation keys', () => {
      // This test ensures TypeScript compilation catches invalid keys
      const validKeys: TranslationKey[] = [
        'hero.title_main',
        'hero.subtitle_main',
        'features.research.title'
      ];

      expect(validKeys).toHaveLength(3);
    });
  });
});
