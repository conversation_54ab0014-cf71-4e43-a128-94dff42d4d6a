/**
 * Test file to demonstrate TypeScript type safety for translation keys
 * This shows how the types prevent typos and provide autocomplete
 */

import { useTranslation } from "@/hooks/useTranslation";
import type { TranslationKey, HeroKeys, FeaturesKeys } from "@/types/translations";

export default function TypeSafetyTest() {
  const { t } = useTranslation();

  // ✅ These will work - valid translation keys
  const validKeys: TranslationKey[] = [
    'hero.title_main',
    'hero.subtitle_main', 
    'features.research.title',
    'legal.belgian_certification'
  ];

  // ❌ These would cause TypeScript errors (uncomment to test)
  // const invalidKeys: TranslationKey[] = [
  //   'hero.invalid_key',        // Error: not a valid key
  //   'nonexistent.key',         // Error: not a valid key
  //   'hero.title_main_typo'     // Error: typo in key name
  // ];

  // ✅ Category-specific types work
  const heroKeys: HeroKeys[] = [
    'hero.title_main',
    'hero.subtitle_main'
  ];

  const featureKeys: FeaturesKeys[] = [
    'features.research.title',
    'features.intake.description'
  ];

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">TypeScript Type Safety Demo</h1>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">✅ Valid Translation Keys:</h2>
        {validKeys.map((key) => (
          <div key={key} className="p-2 bg-green-50 border border-green-200 rounded">
            <code className="text-sm text-green-800">{key}</code>: {t(key)}
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <h2 className="text-lg font-semibold">🎯 Category-Specific Keys:</h2>
        
        <div>
          <h3 className="font-medium">Hero Keys:</h3>
          {heroKeys.map((key) => (
            <div key={key} className="p-2 bg-blue-50 border border-blue-200 rounded">
              <code className="text-sm text-blue-800">{key}</code>: {t(key)}
            </div>
          ))}
        </div>

        <div>
          <h3 className="font-medium">Feature Keys:</h3>
          {featureKeys.map((key) => (
            <div key={key} className="p-2 bg-purple-50 border border-purple-200 rounded">
              <code className="text-sm text-purple-800">{key}</code>: {t(key)}
            </div>
          ))}
        </div>
      </div>

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 className="font-medium text-yellow-800">🛡️ Type Safety Benefits:</h3>
        <ul className="list-disc list-inside text-sm text-yellow-700 mt-2 space-y-1">
          <li>Autocomplete in VS Code for all translation keys</li>
          <li>Compile-time errors for invalid keys</li>
          <li>Refactoring safety - rename keys across codebase</li>
          <li>Category-specific types for better organization</li>
          <li>No more typos in translation key names</li>
        </ul>
      </div>

      <div className="p-4 bg-gray-50 border border-gray-200 rounded">
        <h3 className="font-medium">📊 Current Stats:</h3>
        <p className="text-sm text-gray-600">
          Total translation keys: 39 | Categories: 7 | Type-safe: ✅
        </p>
      </div>
    </div>
  );
}
