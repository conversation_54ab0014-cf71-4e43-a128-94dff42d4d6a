/**
 * Translation Factory Demo Component
 * 
 * Demonstrates the Translation Key Factory functionality
 * including creation, validation, and management of translation keys.
 */

import React, { useState } from 'react';
import TranslationKeyCreator from '@/components/TranslationKeyCreator';
import { useTranslationFactory } from '@/hooks/useTranslationFactory';

export default function TranslationFactoryDemo() {
  const [showCreator, setShowCreator] = useState(false);
  const [createdKeys, setCreatedKeys] = useState<string[]>([]);
  const { quickCreate, isCreating, lastResult } = useTranslationFactory();

  const handleKeyCreated = (key: string) => {
    setCreatedKeys(prev => [...prev, key]);
    setShowCreator(false);
  };

  const handleQuickCreate = async () => {
    const result = await quickCreate(
      'demo.quick_test',
      'This is a quick test key created programmatically',
      'general'
    );
    
    if (result.success) {
      setCreatedKeys(prev => [...prev, result.key!]);
    }
  };

  const examples = [
    {
      title: 'Hero Section Key',
      key: 'hero.new_cta_button',
      content: 'Get Started Today',
      category: 'hero',
      description: 'Call-to-action button for hero section'
    },
    {
      title: 'Belgian Legal Notice',
      key: 'legal.belgian_gdpr_notice',
      content: 'This website complies with Belgian GDPR regulations',
      category: 'legal',
      description: 'GDPR compliance notice for Belgian users'
    },
    {
      title: 'Feature Description',
      key: 'features.ai_document_review',
      content: 'AI-powered document review and analysis',
      category: 'features',
      description: 'Description of AI document review feature'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🏗️ Translation Key Factory Demo
        </h1>
        <p className="text-lg text-gray-600">
          Demonstrates the developer tools for creating and managing translation keys
        </p>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setShowCreator(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            🎨 Open Key Creator
          </button>
          <button
            onClick={handleQuickCreate}
            disabled={isCreating}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-400"
          >
            ⚡ Quick Create Demo Key
          </button>
        </div>
        
        {lastResult && (
          <div className={`mt-4 p-3 rounded-md ${
            lastResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-sm ${lastResult.success ? 'text-green-800' : 'text-red-800'}`}>
              {lastResult.success ? '✅ ' : '❌ '}
              {lastResult.success ? `Key '${lastResult.key}' created!` : lastResult.error}
            </p>
          </div>
        )}
      </div>

      {/* Key Creator */}
      {showCreator && (
        <TranslationKeyCreator
          onKeyCreated={handleKeyCreated}
          onClose={() => setShowCreator(false)}
        />
      )}

      {/* Examples */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Example Translation Keys</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {examples.map((example, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">{example.title}</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Key:</span>
                  <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                    {example.key}
                  </code>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Content:</span>
                  <p className="text-gray-600 mt-1">{example.content}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Category:</span>
                  <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                    {example.category}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Created Keys */}
      {createdKeys.length > 0 && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Recently Created Keys ({createdKeys.length})
          </h2>
          <div className="space-y-2">
            {createdKeys.map((key, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                <code className="text-sm font-mono text-green-800">{key}</code>
                <span className="text-xs text-green-600">✅ Created</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Benefits */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">🎯 Translation Factory Benefits</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">For Developers</h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✅ Easy key creation with validation</li>
              <li>✅ Smart category suggestions</li>
              <li>✅ Automatic TypeScript type updates</li>
              <li>✅ CLI tools for quick creation</li>
              <li>✅ Real-time validation feedback</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">For Content Managers</h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✅ Consistent key naming conventions</li>
              <li>✅ Organized by categories</li>
              <li>✅ Country-specific content support</li>
              <li>✅ Automatic database integration</li>
              <li>✅ Bulk operations support</li>
            </ul>
          </div>
        </div>
      </div>

      {/* CLI Examples */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">🖥️ CLI Usage Examples</h2>
        <div className="space-y-3">
          <div>
            <h3 className="font-medium text-gray-900 mb-1">Basic Key Creation:</h3>
            <code className="block p-3 bg-gray-800 text-green-400 rounded text-sm font-mono">
              npm run create-key "hero.new_title" "Welcome to AiLex"
            </code>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-1">Belgium-Only Content:</h3>
            <code className="block p-3 bg-gray-800 text-green-400 rounded text-sm font-mono">
              npm run create-key "legal.belgian_notice" "Belgian legal notice" --belgium-only
            </code>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-1">Interactive Mode:</h3>
            <code className="block p-3 bg-gray-800 text-green-400 rounded text-sm font-mono">
              npm run create-key:interactive
            </code>
          </div>
        </div>
      </div>
    </div>
  );
}
