/**
 * Test file to demonstrate ESLint hardcoded string prevention
 * This file intentionally contains hardcoded strings to show how ESLint catches them
 */

import { useTranslation } from "@/hooks/useTranslation";

export default function ESLintTest() {
  const { t } = useTranslation();

  return (
    <div className="p-4">
      {/* ❌ This will trigger ESLint errors */}
      <h1>{t('content.this_is_a')}</h1>
      <p>{t('content.this_paragraph_contains')}</p>
      
      {/* ❌ This will also trigger errors */}
      <button>{t('content.click_me')}</button>
      <input placeholder={t('content.enter_your_name')} />
      
      {/* ✅ This is the correct way - using translation system */}
      <h1>{t('test.title')}</h1>
      <p>{t('test.description')}</p>
      <button>{t('test.button_text')}</button>
      <input placeholder={t('test.placeholder')} />
      
      {/* ✅ These are allowed - numbers, symbols, CSS classes */}
      <div className="flex items-center gap-4">
        <span>{t('content.123')}</span>
        <span>+</span>
        <span>-</span>
        <span>.</span>
      </div>
    </div>
  );
}
