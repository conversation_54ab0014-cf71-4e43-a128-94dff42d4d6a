/**
 * Translation Key Factory
 * 
 * Provides utilities for creating, validating, and managing translation keys
 * with automatic database integration and TypeScript type updates.
 */

import { supabase } from '@/lib/supabase';
import type { TranslationKey, TranslationCategory, ContentType, Country, Language } from '@/types/translations';

// Key creation options
export interface CreateTranslationKeyOptions {
  key: string;
  defaultContent: string;
  description?: string;
  category?: TranslationCategory;
  contentType?: ContentType;
  showInCountries?: Country[];
  hideInCountries?: Country[];
  metadata?: {
    priority?: number;
    context?: string;
    styling?: {
      className?: string;
      belgiumClassName?: string;
      usClassName?: string;
    };
  };
}

// Key validation result
export interface KeyValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: {
    key?: string;
    category?: TranslationCategory;
    description?: string;
  };
}

// Factory result
export interface TranslationKeyFactoryResult {
  success: boolean;
  key?: string;
  id?: string;
  error?: string;
  warnings?: string[];
  typesRegenerated?: boolean;
}

/**
 * Translation Key Factory Class
 */
export class TranslationKeyFactory {
  private static instance: TranslationKeyFactory;
  
  public static getInstance(): TranslationKeyFactory {
    if (!TranslationKeyFactory.instance) {
      TranslationKeyFactory.instance = new TranslationKeyFactory();
    }
    return TranslationKeyFactory.instance;
  }

  /**
   * Validate a translation key
   */
  validateKey(key: string, content: string): KeyValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: KeyValidationResult['suggestions'] = {};

    // Key format validation
    if (!key || key.trim().length === 0) {
      errors.push('Key cannot be empty');
    }

    if (!/^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$/.test(key)) {
      errors.push('Key must use lowercase letters, numbers, underscores, and dots only');
      suggestions.key = this.suggestKeyFormat(key);
    }

    if (key.length > 100) {
      errors.push('Key is too long (max 100 characters)');
    }

    if (key.split('.').length > 4) {
      warnings.push('Key has many levels - consider simplifying');
    }

    // Content validation
    if (!content || content.trim().length === 0) {
      errors.push('Default content cannot be empty');
    }

    if (content.length > 1000) {
      warnings.push('Content is very long - consider breaking into smaller keys');
    }

    // Category suggestion
    const suggestedCategory = this.suggestCategory(key);
    if (suggestedCategory) {
      suggestions.category = suggestedCategory;
    }

    // Description suggestion
    if (!suggestions.description) {
      suggestions.description = this.suggestDescription(key, content);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Create a new translation key
   */
  async createKey(options: CreateTranslationKeyOptions): Promise<TranslationKeyFactoryResult> {
    try {
      // Validate the key
      const validation = this.validateKey(options.key, options.defaultContent);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      // Check if key already exists
      const { data: existingKey } = await supabase
        .from('translation_keys')
        .select('id')
        .eq('key', options.key)
        .single();

      if (existingKey) {
        return {
          success: false,
          error: `Translation key '${options.key}' already exists`
        };
      }

      // Get or create category
      const categoryId = await this.ensureCategory(options.category || validation.suggestions.category || 'general');

      // Create the translation key
      const { data: newKey, error: createError } = await supabase
        .from('translation_keys')
        .insert({
          key: options.key,
          category_id: categoryId,
          description: options.description || validation.suggestions.description,
          default_content: options.defaultContent,
          show_in_countries: options.showInCountries || ['US', 'BE'],
          hide_in_countries: options.hideInCountries || [],
          content_type: options.contentType || 'shared',
          metadata: options.metadata || {}
        })
        .select()
        .single();

      if (createError) {
        return {
          success: false,
          error: `Failed to create translation key: ${createError.message}`
        };
      }

      // Regenerate TypeScript types
      let typesRegenerated = false;
      try {
        await this.regenerateTypes();
        typesRegenerated = true;
      } catch (typeError) {
        console.warn('Failed to regenerate types:', typeError);
      }

      return {
        success: true,
        key: options.key,
        id: newKey.id,
        warnings: validation.warnings,
        typesRegenerated
      };

    } catch (error) {
      return {
        success: false,
        error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Batch create multiple translation keys
   */
  async createKeys(keyOptions: CreateTranslationKeyOptions[]): Promise<TranslationKeyFactoryResult[]> {
    const results: TranslationKeyFactoryResult[] = [];
    
    for (const options of keyOptions) {
      const result = await this.createKey(options);
      results.push(result);
    }

    return results;
  }

  /**
   * Quick create with smart defaults
   */
  async quickCreate(key: string, content: string, category?: TranslationCategory): Promise<TranslationKeyFactoryResult> {
    const validation = this.validateKey(key, content);
    
    return this.createKey({
      key,
      defaultContent: content,
      category: category || validation.suggestions.category,
      description: validation.suggestions.description,
      contentType: this.inferContentType(key),
      showInCountries: this.inferCountries(key)
    });
  }

  // Private helper methods
  private suggestKeyFormat(key: string): string {
    return key
      .toLowerCase()
      .replace(/[^a-z0-9.]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  private suggestCategory(key: string): TranslationCategory | undefined {
    const [firstPart] = key.split('.');
    
    const categoryMap: Record<string, TranslationCategory> = {
      'hero': 'hero',
      'features': 'features',
      'problems': 'problems',
      'solutions': 'solutions',
      'legal': 'legal',
      'seo': 'seo',
      'nav': 'general',
      'footer': 'general',
      'form': 'general',
      'button': 'general',
      'error': 'general',
      'success': 'general'
    };

    return categoryMap[firstPart];
  }

  private suggestDescription(key: string, content: string): string {
    const [category, ...parts] = key.split('.');
    const context = parts.join(' ');
    
    if (content.length < 50) {
      return `${category} section: ${context || 'content'}`;
    } else {
      return `${category} section: ${context || 'content'} (${content.substring(0, 30)}...)`;
    }
  }

  private inferContentType(key: string): ContentType {
    if (key.includes('belgian') || key.includes('belgium')) {
      return 'belgium-only';
    }
    if (key.includes('us') || key.includes('american')) {
      return 'us-only';
    }
    return 'shared';
  }

  private inferCountries(key: string): Country[] {
    const contentType = this.inferContentType(key);
    switch (contentType) {
      case 'belgium-only': return ['BE'];
      case 'us-only': return ['US'];
      default: return ['US', 'BE'];
    }
  }

  private async ensureCategory(categoryName: string): Promise<string> {
    // Try to get existing category
    const { data: existingCategory } = await supabase
      .from('translation_categories')
      .select('id')
      .eq('name', categoryName)
      .single();

    if (existingCategory) {
      return existingCategory.id;
    }

    // Create new category
    const { data: newCategory, error } = await supabase
      .from('translation_categories')
      .insert({
        name: categoryName,
        description: `Auto-created category for ${categoryName} content`,
        is_active: true,
        display_order: 999
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`);
    }

    return newCategory.id;
  }

  private async regenerateTypes(): Promise<void> {
    // This would trigger the type generation script
    // In a real implementation, this could call the script or API endpoint
    console.log('🔄 TypeScript types should be regenerated');
    // For now, we'll just log - in production this could trigger the script
  }
}

// Export singleton instance
export const translationKeyFactory = TranslationKeyFactory.getInstance();
