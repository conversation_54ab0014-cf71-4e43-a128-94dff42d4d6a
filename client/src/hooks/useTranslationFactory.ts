/**
 * React hook for using the Translation Key Factory
 * 
 * Provides easy access to translation key creation and management
 * from within React components.
 */

import { useState, useCallback } from 'react';
import { translationKeyFactory } from '@/lib/translationKeyFactory';
import type { 
  CreateTranslationKeyOptions, 
  TranslationKeyFactoryResult,
  KeyValidationResult 
} from '@/lib/translationKeyFactory';

export interface UseTranslationFactoryReturn {
  // State
  isCreating: boolean;
  lastResult: TranslationKeyFactoryResult | null;
  validationResult: KeyValidationResult | null;
  
  // Actions
  createKey: (options: CreateTranslationKeyOptions) => Promise<TranslationKeyFactoryResult>;
  quickCreate: (key: string, content: string, category?: string) => Promise<TranslationKeyFactoryResult>;
  validateKey: (key: string, content: string) => KeyValidationResult;
  batchCreate: (keys: CreateTranslationKeyOptions[]) => Promise<TranslationKeyFactoryResult[]>;
  
  // Utilities
  clearResult: () => void;
  clearValidation: () => void;
}

/**
 * Hook for using the Translation Key Factory
 */
export function useTranslationFactory(): UseTranslationFactoryReturn {
  const [isCreating, setIsCreating] = useState(false);
  const [lastResult, setLastResult] = useState<TranslationKeyFactoryResult | null>(null);
  const [validationResult, setValidationResult] = useState<KeyValidationResult | null>(null);

  const createKey = useCallback(async (options: CreateTranslationKeyOptions): Promise<TranslationKeyFactoryResult> => {
    setIsCreating(true);
    setLastResult(null);
    
    try {
      const result = await translationKeyFactory.createKey(options);
      setLastResult(result);
      return result;
    } catch (error) {
      const errorResult: TranslationKeyFactoryResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      setLastResult(errorResult);
      return errorResult;
    } finally {
      setIsCreating(false);
    }
  }, []);

  const quickCreate = useCallback(async (key: string, content: string, category?: string): Promise<TranslationKeyFactoryResult> => {
    setIsCreating(true);
    setLastResult(null);
    
    try {
      const result = await translationKeyFactory.quickCreate(key, content, category as any);
      setLastResult(result);
      return result;
    } catch (error) {
      const errorResult: TranslationKeyFactoryResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      setLastResult(errorResult);
      return errorResult;
    } finally {
      setIsCreating(false);
    }
  }, []);

  const validateKey = useCallback((key: string, content: string): KeyValidationResult => {
    const result = translationKeyFactory.validateKey(key, content);
    setValidationResult(result);
    return result;
  }, []);

  const batchCreate = useCallback(async (keys: CreateTranslationKeyOptions[]): Promise<TranslationKeyFactoryResult[]> => {
    setIsCreating(true);
    setLastResult(null);
    
    try {
      const results = await translationKeyFactory.createKeys(keys);
      
      // Set the last result to a summary
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;
      
      setLastResult({
        success: failureCount === 0,
        key: `Batch operation: ${successCount} success, ${failureCount} failed`,
        error: failureCount > 0 ? `${failureCount} keys failed to create` : undefined
      });
      
      return results;
    } catch (error) {
      const errorResult: TranslationKeyFactoryResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      setLastResult(errorResult);
      return [errorResult];
    } finally {
      setIsCreating(false);
    }
  }, []);

  const clearResult = useCallback(() => {
    setLastResult(null);
  }, []);

  const clearValidation = useCallback(() => {
    setValidationResult(null);
  }, []);

  return {
    isCreating,
    lastResult,
    validationResult,
    createKey,
    quickCreate,
    validateKey,
    batchCreate,
    clearResult,
    clearValidation
  };
}

/**
 * Hook for quick key creation with smart defaults
 */
export function useQuickTranslationKey() {
  const { quickCreate, isCreating, lastResult } = useTranslationFactory();
  
  const createQuickKey = useCallback(async (key: string, content: string) => {
    return await quickCreate(key, content);
  }, [quickCreate]);

  return {
    createKey: createQuickKey,
    isCreating,
    result: lastResult
  };
}

/**
 * Hook for validating translation keys in real-time
 */
export function useTranslationKeyValidation() {
  const { validateKey, validationResult, clearValidation } = useTranslationFactory();
  
  const validateInRealTime = useCallback((key: string, content: string) => {
    if (!key && !content) {
      clearValidation();
      return null;
    }
    return validateKey(key, content);
  }, [validateKey, clearValidation]);

  return {
    validate: validateInRealTime,
    result: validationResult,
    isValid: validationResult?.isValid ?? null,
    errors: validationResult?.errors ?? [],
    warnings: validationResult?.warnings ?? [],
    suggestions: validationResult?.suggestions ?? {}
  };
}
