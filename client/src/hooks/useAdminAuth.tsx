import { useState, useEffect } from "react";
import { useLocation } from "wouter";

interface AdminUser {
  id: string;
  email: string;
  name: string;
}

interface UseAdminAuthReturn {
  user: AdminUser | null;
  loading: boolean;
  logout: () => Promise<void>;
}

export function useAdminAuth(): UseAdminAuthReturn {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [, setLocation] = useLocation();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/admin/auth?action=verify", {
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        setUser(null);
        // Redirect to login if not authenticated
        setLocation("/admin-secret-portal");
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
      setLocation("/admin-secret-portal");
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch("/api/admin/auth?action=logout", {
        method: "POST",
        credentials: "include",
      });
      setUser(null);
      setLocation("/admin-secret-portal");
    } catch (error) {
      console.error("Logout failed:", error);
      // Force redirect even if logout API fails
      setUser(null);
      setLocation("/admin-secret-portal");
    }
  };

  return { user, loading, logout };
}

// Higher-order component for protecting admin routes
export function withAdminAuth<T extends object>(Component: React.ComponentType<T>) {
  return function ProtectedComponent(props: T) {
    const { user, loading } = useAdminAuth();

    if (loading) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Verifying authentication...</p>
          </div>
        </div>
      );
    }

    if (!user) {
      return null; // Will redirect to login
    }

    return <Component {...props} />;
  };
}
