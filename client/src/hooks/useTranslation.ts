import { useTranslation as useTranslationContext } from '@/contexts/TranslationContext';
import type { Country, Language, TranslationOptions } from '@/contexts/TranslationContext';
import type { TranslationKey, TranslationFunction } from '@/types/translations';

// Re-export the main hook
export { useTranslation } from '@/contexts/TranslationContext';

// Convenience hook for specific country/language
export function useCountryTranslation(country: Country, language: Language) {
  const { t, tShow, tWithMeta } = useTranslationContext();
  
  return {
    t: (key: TranslationKey, options?: Omit<TranslationOptions, 'country'>) =>
      t(key, { ...options, country }),
    tShow: (key: TranslationKey, options?: Omit<TranslationOptions, 'country'>) =>
      tShow(key, { ...options, country }),
    tWithMeta: (key: TranslationKey, options?: Omit<TranslationOptions, 'country'>) =>
      tWithMeta(key, { ...options, country }),
    country,
    language
  };
}

// Hook for Belgium-specific translations
export function useBelgiumTranslation(language: Language = 'fr') {
  return useCountryTranslation('BE', language);
}

// Hook for US translations
export function useUSTranslation(language: Language = 'en') {
  return useCountryTranslation('US', language);
}

// Utility functions for common patterns
export const translationUtils = {
  // Check if key exists
  keyExists: (key: string, translationKeys: any[]) => {
    return translationKeys.some(tk => tk.key === key);
  },
  
  // Get all keys for a category
  getKeysByCategory: (category: string, translationKeys: any[]) => {
    return translationKeys.filter(tk => tk.category === category);
  },
  
  // Get Belgium-only keys
  getBelgiumOnlyKeys: (translationKeys: any[]) => {
    return translationKeys.filter(tk => tk.contentType === 'belgium-only');
  },
  
  // Get US-only keys
  getUSOnlyKeys: (translationKeys: any[]) => {
    return translationKeys.filter(tk => tk.contentType === 'us-only');
  },
  
  // Get shared keys
  getSharedKeys: (translationKeys: any[]) => {
    return translationKeys.filter(tk => tk.contentType === 'shared');
  }
};

// Type exports
export type { Country, Language, TranslationOptions } from '@/contexts/TranslationContext';
