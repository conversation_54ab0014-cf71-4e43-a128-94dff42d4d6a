import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useGeoLocation } from './useGeoLocation';
import type { Country, Language } from '@/types';

// Hook for automatic location-based redirection
export function useAutoRedirect() {
  const [location, setLocation] = useLocation();
  const { country, suggestedLanguage, isLoading } = useGeoLocation();
  const [hasRedirected, setHasRedirected] = useState(false);

  // Check if user has opted out of auto-redirect
  const hasOptedOut = (): boolean => {
    try {
      const optOut = localStorage.getItem('ailex-auto-redirect-opt-out');
      if (!optOut) return false;
      
      const optOutData = JSON.parse(optOut);
      // Check if opt-out is less than 30 days old
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      return optOutData.timestamp > thirtyDaysAgo;
    } catch {
      return false;
    }
  };

  // Store opt-out preference
  const optOutOfAutoRedirect = () => {
    try {
      const optOutData = {
        timestamp: Date.now(),
        currentPath: location
      };
      localStorage.setItem('ailex-auto-redirect-opt-out', JSON.stringify(optOutData));
      console.log('🚫 User opted out of auto-redirect');
    } catch (error) {
      console.warn('Failed to store opt-out preference:', error);
    }
  };

  // Get target path based on detected location and language
  const getTargetPath = (detectedCountry: Country, detectedLanguage: Language): string => {
    if (detectedCountry === 'BE') {
      if (detectedLanguage === 'fr') return '/be/fr';
      if (detectedLanguage === 'nl') return '/be/nl';
      return '/be'; // Belgian English
    }
    return '/'; // US site
  };

  // Check if current path matches target path
  const isOnCorrectPath = (currentPath: string, targetPath: string): boolean => {
    // Exact match - this is the main case
    if (currentPath === targetPath) return true;

    // If target is Belgian English (/be) and user is already on any Belgian page, don't redirect
    if (targetPath === '/be' && currentPath.startsWith('/be')) return true;

    // For all other cases, require exact match
    return false;
  };

  // Check if we should redirect
  const shouldRedirect = (
    currentPath: string,
    detectedCountry: Country | null,
    detectedLanguage: Language
  ): boolean => {
    // Don't redirect if no country detected
    if (!detectedCountry) return false;
    
    // Don't redirect if user has opted out
    if (hasOptedOut()) return false;
    
    // Don't redirect if already redirected in this session
    if (hasRedirected) return false;
    
    // Don't redirect on admin pages
    if (currentPath.startsWith('/admin')) return false;
    
    // Don't redirect on test pages
    if (currentPath.startsWith('/test-')) return false;
    
    // Get target path
    const targetPath = getTargetPath(detectedCountry, detectedLanguage);

    // Don't redirect if already on correct path
    const onCorrectPath = isOnCorrectPath(currentPath, targetPath);

    console.log(`🔍 Redirect check: ${currentPath} → ${targetPath} (correct: ${onCorrectPath})`);

    if (onCorrectPath) return false;

    return true;
  };

  // Main redirection logic
  useEffect(() => {
    if (isLoading || !country) return;

    const shouldPerformRedirect = shouldRedirect(location, country, suggestedLanguage);
    
    if (shouldPerformRedirect) {
      const targetPath = getTargetPath(country, suggestedLanguage);

      console.log(`🌍 Auto-redirecting: ${country} (${suggestedLanguage}) → ${targetPath}`);
      console.log(`📍 Current path: ${location} → Target path: ${targetPath}`);

      // Perform redirect
      setLocation(targetPath);
      setHasRedirected(true);
      
      // Store that we've redirected this user
      try {
        const redirectData = {
          from: location,
          to: targetPath,
          country,
          language: suggestedLanguage,
          timestamp: Date.now()
        };
        localStorage.setItem('ailex-last-redirect', JSON.stringify(redirectData));
      } catch (error) {
        console.warn('Failed to store redirect data:', error);
      }
    }
  }, [location, country, suggestedLanguage, isLoading, hasRedirected, setLocation]);

  return {
    isRedirecting: isLoading && country !== null,
    hasRedirected,
    optOutOfAutoRedirect,
    hasOptedOut: hasOptedOut()
  };
}

// Utility functions
export const autoRedirectUtils = {
  // Get last redirect info
  getLastRedirect: () => {
    try {
      const data = localStorage.getItem('ailex-last-redirect');
      return data ? JSON.parse(data) : null;
    } catch {
      return null;
    }
  },
  
  // Clear redirect history
  clearRedirectHistory: () => {
    localStorage.removeItem('ailex-last-redirect');
    localStorage.removeItem('ailex-auto-redirect-opt-out');
  },
  
  // Check if user was redirected recently
  wasRedirectedRecently: (minutes: number = 5) => {
    const lastRedirect = autoRedirectUtils.getLastRedirect();
    if (!lastRedirect) return false;
    
    const recentTime = Date.now() - (minutes * 60 * 1000);
    return lastRedirect.timestamp > recentTime;
  }
};
