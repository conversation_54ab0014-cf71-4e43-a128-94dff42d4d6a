import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import type { Country, Language } from '@/types';
import type { TranslationKey as TranslationKeyType, TranslationFunction } from '@/types/translations';
import { TranslationService } from '@/services/translationService';

// Enhanced translation key interface
export interface TranslationKey {
  id: string;
  key: string;
  category: string;
  description: string;
  defaultContent: string; // US English source of truth
  showInCountries: Country[];
  hideInCountries: Country[];
  contentType: 'shared' | 'country-specific' | 'belgium-only' | 'us-only';
  metadata?: {
    belgiumVariant?: string;
    legalContext?: boolean;
    priority?: number;
    styling?: {
      belgiumClassName?: string;
      usClassName?: string;
    };
  };
  created_at: string;
  updated_at: string;
}

// Translation interface
export interface Translation {
  id: string;
  keyId: string;
  language: Language;
  content: string;
  context?: string;
  isAutoTranslated: boolean;
  confidence?: number;
  created_at: string;
  updated_at: string;
}

// Translation options
export interface TranslationOptions {
  country?: Country;
  fallback?: string;
  variables?: Record<string, string>;
  returnMetadata?: boolean;
}

// Translation result with metadata
export interface TranslationResult {
  content: string;
  isVisible: boolean;
  metadata?: TranslationKey['metadata'];
  className?: string;
}

// Context type
interface TranslationContextType {
  // Core translation function with type safety
  t: TranslationFunction;

  // Visibility check function
  tShow: (key: TranslationKeyType, options?: TranslationOptions) => boolean;

  // Enhanced function with metadata
  tWithMeta: (key: TranslationKeyType, options?: TranslationOptions) => TranslationResult;
  
  // Current context
  country: Country;
  language: Language;
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Data
  translationKeys: TranslationKey[];
  translations: Translation[];
  
  // Actions
  setCountry: (country: Country) => void;
  setLanguage: (language: Language) => void;
  refreshTranslations: () => Promise<void>;
}

// Create context
const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Provider props
interface TranslationProviderProps {
  children: ReactNode;
  initialCountry?: Country;
  initialLanguage?: Language;
}

// Translation provider component
export function TranslationProvider({ 
  children, 
  initialCountry = 'US', 
  initialLanguage = 'en' 
}: TranslationProviderProps) {
  // State
  const [country, setCountry] = useState<Country>(initialCountry);
  const [language, setLanguage] = useState<Language>(initialLanguage);
  const [translationKeys, setTranslationKeys] = useState<TranslationKey[]>([]);
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch translation keys with country filtering
  const fetchTranslationKeys = async () => {
    try {
      const { keys } = await TranslationService.getTranslationKeys({
        country: country || undefined
      });
      setTranslationKeys(keys);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch translation keys');
    }
  };

  // Fetch translations
  const fetchTranslations = async () => {
    try {
      const { translations } = await TranslationService.getTranslations();
      setTranslations(translations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch translations');
    }
  };

  // Refresh all translation data
  const refreshTranslations = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([fetchTranslationKeys(), fetchTranslations()]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on mount and when language or country changes
  useEffect(() => {
    refreshTranslations();
  }, [language, country]);

  // Check if content should be visible for current country
  const isContentVisible = (key: TranslationKey, targetCountry?: Country): boolean => {
    const checkCountry = targetCountry || country;
    
    // Check hide rules first
    if (key.hideInCountries.includes(checkCountry)) {
      return false;
    }
    
    // Check show rules
    if (key.showInCountries.length > 0) {
      return key.showInCountries.includes(checkCountry);
    }
    
    // Default visibility based on content type
    switch (key.contentType) {
      case 'us-only':
        return checkCountry === 'US';
      case 'belgium-only':
        return checkCountry === 'BE';
      case 'shared':
      case 'country-specific':
      default:
        return true;
    }
  };

  // Find translation for key and language
  const findTranslation = (keyId: string, targetLanguage?: Language): Translation | null => {
    const checkLanguage = targetLanguage || language;
    return translations.find(t => t.keyId === keyId && t.language === checkLanguage) || null;
  };

  // Get content for a translation key
  const getTranslationContent = (
    key: TranslationKey, 
    targetLanguage?: Language,
    targetCountry?: Country
  ): string => {
    const checkLanguage = targetLanguage || language;
    const checkCountry = targetCountry || country;
    
    // For English or US country, always return default content
    if (checkLanguage === 'en' || checkCountry === 'US') {
      return key.defaultContent;
    }
    
    // Look for translation
    const translation = findTranslation(key.id, checkLanguage);
    
    if (translation) {
      return translation.content;
    }
    
    // Fallback to default content (US English)
    return key.defaultContent;
  };

  // Core translation function
  const t = (key: string, options: TranslationOptions = {}): string => {
    const translationKey = translationKeys.find(tk => tk.key === key);
    
    if (!translationKey) {
      console.warn(`Translation key not found: ${key}`);
      return options.fallback || key;
    }
    
    // Check visibility
    if (!isContentVisible(translationKey, options.country)) {
      return options.fallback || '';
    }
    
    let content = getTranslationContent(translationKey, language, options.country);
    
    // Apply variables if provided
    if (options.variables) {
      Object.entries(options.variables).forEach(([variable, value]) => {
        content = content.replace(new RegExp(`{{${variable}}}`, 'g'), value);
      });
    }
    
    return content;
  };

  // Visibility check function
  const tShow = (key: string, options: TranslationOptions = {}): boolean => {
    const translationKey = translationKeys.find(tk => tk.key === key);
    
    if (!translationKey) {
      return false;
    }
    
    return isContentVisible(translationKey, options.country);
  };

  // Enhanced function with metadata
  const tWithMeta = (key: string, options: TranslationOptions = {}): TranslationResult => {
    const translationKey = translationKeys.find(tk => tk.key === key);
    
    if (!translationKey) {
      return {
        content: options.fallback || key,
        isVisible: false
      };
    }
    
    const isVisible = isContentVisible(translationKey, options.country);
    const content = isVisible ? getTranslationContent(translationKey, language, options.country) : '';
    
    // Apply variables if provided
    let finalContent = content;
    if (options.variables && content) {
      Object.entries(options.variables).forEach(([variable, value]) => {
        finalContent = finalContent.replace(new RegExp(`{{${variable}}}`, 'g'), value);
      });
    }
    
    // Determine className based on country
    let className = '';
    if (translationKey.metadata?.styling) {
      const targetCountry = options.country || country;
      if (targetCountry === 'BE' && translationKey.metadata.styling.belgiumClassName) {
        className = translationKey.metadata.styling.belgiumClassName;
      } else if (targetCountry === 'US' && translationKey.metadata.styling.usClassName) {
        className = translationKey.metadata.styling.usClassName;
      }
    }
    
    return {
      content: finalContent,
      isVisible,
      metadata: translationKey.metadata,
      className
    };
  };

  // Context value
  const value: TranslationContextType = {
    t,
    tShow,
    tWithMeta,
    country,
    language,
    isLoading,
    error,
    translationKeys,
    translations,
    setCountry,
    setLanguage,
    refreshTranslations
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
}

// Custom hook to use translation context
export function useTranslation() {
  const context = useContext(TranslationContext);
  
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  
  return context;
}

// Export types
export type { TranslationContextType, TranslationOptions, TranslationResult };
