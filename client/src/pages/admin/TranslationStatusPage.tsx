/**
 * Translation Status Admin Page
 * 
 * Dedicated admin page for the Translation Status Dashboard
 * with navigation and admin authentication.
 */

import React from 'react';
import { <PERSON> } from 'wouter';
import { ArrowLeft, Home, Settings } from 'lucide-react';
import { withAdminAuth, useAdminAuth } from '@/hooks/useAdminAuth';
import TranslationStatusDashboard from '@/components/TranslationStatusDashboard';

function TranslationStatusPage() {
  const { user, logout } = useAdminAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/admin">
                  <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                    <ArrowLeft className="h-5 w-5 mr-2" />
                    Back to Admin
                  </a>
                </Link>
                <div className="h-6 border-l border-gray-300"></div>
                <h1 className="text-xl font-semibold text-gray-900">Translation Status</h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <Link href="/">
                  <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                    <Home className="h-5 w-5 mr-2" />
                    Home
                  </a>
                </Link>
                
                <div className="flex items-center space-x-3">
                  <div className="text-sm text-gray-600">
                    Welcome, <span className="font-medium">{user?.email}</span>
                  </div>
                  <button
                    onClick={logout}
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <Link href="/admin/translation-status">
              <a className="border-b-2 border-indigo-500 text-indigo-600 py-4 px-1 text-sm font-medium">
                📊 Status Dashboard
              </a>
            </Link>
            <Link href="/admin/translations">
              <a className="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium">
                🌐 Translation Management
              </a>
            </Link>
            <Link href="/admin/translation-bulk">
              <a className="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium">
                🔧 Bulk Operations
              </a>
            </Link>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <TranslationStatusDashboard />
      </div>

      {/* Quick Actions Sidebar */}
      <div className="fixed bottom-6 right-6 space-y-3">
        <Link href="/admin/translations">
          <a className="block bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors">
            <Settings className="h-6 w-6" />
          </a>
        </Link>
        <Link href="/translation-factory-demo">
          <a className="block bg-green-600 text-white p-3 rounded-full shadow-lg hover:bg-green-700 transition-colors">
            <span className="text-lg">🏗️</span>
          </a>
        </Link>
      </div>

      {/* Help Text */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-blue-800 font-medium mb-2">💡 Dashboard Help</h3>
          <div className="text-blue-700 text-sm space-y-1">
            <p>• <strong>System Health:</strong> Shows critical issues that need immediate attention</p>
            <p>• <strong>Missing Translations:</strong> High-priority items are Belgium-specific content</p>
            <p>• <strong>Language Coverage:</strong> Track translation progress across French and Dutch</p>
            <p>• <strong>Category Overview:</strong> See which content areas need translation work</p>
            <p>• <strong>Recent Activity:</strong> Monitor translation system changes</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAdminAuth(TranslationStatusPage);
