import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "wouter";
import { LogOut, User } from "lucide-react";
import type { TranslationKey, Translation, Language } from "@/types";
import { withAdminAuth, useAdminAuth } from "@/hooks/useAdminAuth";
import { TranslationService } from "@/services/translationService";

// Translation management interface
function TranslationManagement() {
  const { user, logout } = useAdminAuth();
  const [translationKeys, setTranslationKeys] = useState<TranslationKey[]>([]);
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<Language>("fr");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showCreateKeyModal, setShowCreateKeyModal] = useState(false);
  const [showTranslateModal, setShowTranslateModal] = useState(false);
  const [selectedKey, setSelectedKey] = useState<TranslationKey | null>(null);

  // Form states
  const [newKey, setNewKey] = useState({
    key: "",
    description: "",
    category: "",
    defaultContent: "",
  });

  const [translationForm, setTranslationForm] = useState({
    content: "",
    context: "",
  });

  // Bulk operations state
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());
  const [bulkTranslating, setBulkTranslating] = useState(false);

  const categories = ["hero", "pricing", "features", "testimonials", "footer", "navigation", "belgium", "problems", "solutions"];

  useEffect(() => {
    fetchTranslationKeys();
  }, [selectedCategory, searchTerm]);

  useEffect(() => {
    if (selectedLanguage !== "en") {
      fetchTranslations();
    }
  }, [selectedLanguage]);

  const fetchTranslationKeys = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategory !== "all") params.append("category", selectedCategory);
      if (searchTerm) params.append("search", searchTerm);

      const response = await fetch(`/api/translations/keys?${params}`);
      const data = await response.json();

      if (response.ok) {
        setTranslationKeys(data.keys);
      } else {
        setError(data.error || "Failed to fetch translation keys");
      }
    } catch (err) {
      setError("Network error while fetching translation keys");
    } finally {
      setLoading(false);
    }
  };

  const fetchTranslations = async () => {
    try {
      const [translationsResult, keysResult] = await Promise.all([
        TranslationService.getTranslations({ language: selectedLanguage }),
        TranslationService.getTranslationKeys()
      ]);

      setTranslations(translationsResult.translations);
      setTranslationKeys(keysResult.keys);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch translations");
    }
  };

  const createTranslationKey = async () => {
    try {
      const { key } = await TranslationService.createTranslationKey({
        key: newKey.key,
        category: newKey.category,
        description: newKey.description,
        defaultContent: newKey.defaultContent,
      });

      setTranslationKeys([...translationKeys, key]);
      setShowCreateKeyModal(false);
      setNewKey({ key: "", description: "", category: "", defaultContent: "" });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create translation key");
    }
  };

  const autoTranslate = async (keyId: string, text: string, targetLanguage: Language) => {
    try {
      const { results } = await TranslationService.autoTranslate([keyId], targetLanguage);
      const result = results[0];

      if (result.success) {
        // Create the translation with auto-translated content
        await createTranslation(keyId, targetLanguage, result.translatedText, true, result.confidence);
      } else {
        setError("Auto-translation failed");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Auto-translation failed");
    }
  };

  const createTranslation = async (
    keyId: string,
    language: Language,
    content: string,
    isAutoTranslated = false,
    confidence?: number
  ) => {
    try {
      const { translation } = await TranslationService.createTranslation({
        keyId,
        language,
        content,
        context: translationForm.context,
        isAutoTranslated,
        confidence,
      });

      setTranslations([...translations, translation]);
      setShowTranslateModal(false);
      setTranslationForm({ content: "", context: "" });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create translation");
    }
  };

  const getTranslationForKey = (keyId: string): Translation | undefined => {
    return translations.find(t => t.keyId === keyId && t.language === selectedLanguage);
  };

  // Local editing state to prevent cursor jumping
  const [editingStates, setEditingStates] = useState<Record<string, string>>({});
  const [saveTimeouts, setSaveTimeouts] = useState<Record<string, NodeJS.Timeout>>({});

  const updateTranslationContent = (translationId: string, newContent: string) => {
    // Update local editing state immediately (for UI responsiveness)
    setEditingStates(prev => ({ ...prev, [translationId]: newContent }));

    // Update the main translations state immediately to prevent cursor jumping
    setTranslations(prev => prev.map(t =>
      t.id === translationId ? { ...t, content: newContent } : t
    ));

    // Debounce the API call to avoid too many requests
    if (saveTimeouts[translationId]) {
      clearTimeout(saveTimeouts[translationId]);
    }

    const timeoutId = setTimeout(async () => {
      try {
        const response = await fetch(`/api/translations/${translationId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ content: newContent }),
        });

        if (!response.ok) {
          const data = await response.json();
          setError(data.error || "Failed to update translation");
        }
      } catch (err) {
        setError("Network error while updating translation");
      } finally {
        // Clean up editing state and timeout
        setEditingStates(prev => {
          const newState = { ...prev };
          delete newState[translationId];
          return newState;
        });
        setSaveTimeouts(prev => {
          const newState = { ...prev };
          delete newState[translationId];
          return newState;
        });
      }
    }, 500); // 500ms debounce

    setSaveTimeouts(prev => ({ ...prev, [translationId]: timeoutId }));
  };

  const bulkAutoTranslate = async (targetLanguage: Language) => {
    if (selectedKeys.size === 0) {
      setError("Please select keys to translate");
      return;
    }

    setBulkTranslating(true);
    setError(null);

    try {
      const response = await fetch("/api/translations/bulk-auto-translate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          keyIds: Array.from(selectedKeys),
          targetLanguage,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        let successCount = 0;
        let failCount = 0;
        const newTranslations = [];

        // Create translations for each successful result
        for (const result of data.results) {
          if (result.success) {
            try {
              // Call the API to save the translation
              const translationResponse = await fetch("/api/translations", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  keyId: result.keyId,
                  language: targetLanguage,
                  content: result.translatedText,
                  context: "",
                  isAutoTranslated: true,
                  confidence: result.confidence,
                }),
              });

              const translationData = await translationResponse.json();

              if (translationResponse.ok) {
                newTranslations.push(translationData.translation);
                successCount++;
              } else {
                console.error('Failed to save translation for key:', result.keyId, translationData.error);
                failCount++;
              }
            } catch (err) {
              console.error('Network error saving translation for key:', result.keyId, err);
              failCount++;
            }
          } else {
            failCount++;
          }
        }

        // Update translations state with all new translations at once
        if (newTranslations.length > 0) {
          console.log('📝 Adding new translations to state:', newTranslations);
          setTranslations(prev => {
            const updated = [...prev, ...newTranslations];
            console.log('📝 Updated translations state:', updated);
            return updated;
          });
        }

        // Show success message
        if (successCount > 0) {
          setError(null);
          console.log(`✅ Successfully translated ${successCount} keys to ${targetLanguage}`);
        }

        if (failCount > 0) {
          setError(`${failCount} translations failed to save. Please try again.`);
        }

        setSelectedKeys(new Set()); // Clear selection
      } else {
        setError(data.error || "Bulk auto-translation failed");
      }
    } catch (err) {
      setError("Network error during bulk auto-translation");
    } finally {
      setBulkTranslating(false);
    }
  };

  const toggleKeySelection = (keyId: string) => {
    const newSelection = new Set(selectedKeys);
    if (newSelection.has(keyId)) {
      newSelection.delete(keyId);
    } else {
      newSelection.add(keyId);
    }
    setSelectedKeys(newSelection);
  };

  const selectAllKeys = () => {
    const allKeyIds = new Set(translationKeys.map(key => key.id));
    setSelectedKeys(allKeyIds);
  };

  const clearSelection = () => {
    setSelectedKeys(new Set());
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading translation management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Translation Management</h1>
                <p className="mt-2 text-gray-600">Manage translations for AiLex Belgium</p>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <User className="w-4 h-4" />
                  <span>{user?.name}</span>
                </div>
                <Link
                  href="/admin"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  ← Back to Admin
                </Link>
                <button
                  onClick={logout}
                  className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex flex-wrap gap-4 items-center">
              {/* Language selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Language
                </label>
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value as Language)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="en">English (Default)</option>
                  <option value="fr">French</option>
                  <option value="nl">Dutch</option>
                </select>
              </div>

              {/* Category filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="all">All Categories</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>

              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search keys..."
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm w-64"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 items-center">
              {selectedKeys.size > 0 && (
                <div className="text-sm text-gray-600 bg-gray-100 px-3 py-2 rounded-md">
                  {selectedKeys.size} key{selectedKeys.size !== 1 ? 's' : ''} selected
                </div>
              )}
              <button
                onClick={() => setShowCreateKeyModal(true)}
                className="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
              >
                Add Translation Key
              </button>
            </div>
          </div>

          {/* Bulk Operations - Only show for non-English languages */}
          {selectedKeys.size > 0 && selectedLanguage !== "en" && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedKeys.size} key{selectedKeys.size !== 1 ? 's' : ''} selected
                  </span>
                  <button
                    onClick={clearSelection}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Clear selection
                  </button>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-blue-700">
                    Bulk auto-translate to {selectedLanguage === "fr" ? "French" : "Dutch"}:
                  </span>
                  <button
                    onClick={() => bulkAutoTranslate(selectedLanguage)}
                    disabled={bulkTranslating}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 font-medium"
                  >
                    {bulkTranslating ? "Translating..." : `Auto-Translate ${selectedKeys.size} Key${selectedKeys.size !== 1 ? 's' : ''}`}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Translation keys table with language columns */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    checked={selectedKeys.size === translationKeys.length && translationKeys.length > 0}
                    onChange={(e) => e.target.checked ? selectAllKeys() : clearSelection()}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                  Key / Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  🇺🇸 English (Default)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  🇫🇷 French
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  🇳🇱 Dutch
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {translationKeys.map((key) => {
                const frTranslation = translations.find(t => t.keyId === key.id && t.language === "fr");
                const nlTranslation = translations.find(t => t.keyId === key.id && t.language === "nl");

                // Debug logging
                if (key.id === '1' || key.id === '2') {
                  console.log(`🔍 Key ${key.id} (${key.key}):`, {
                    frTranslation,
                    nlTranslation,
                    allTranslations: translations.filter(t => t.keyId === key.id)
                  });
                }

                return (
                  <tr key={key.id} className="hover:bg-gray-50">
                    {/* Checkbox */}
                    <td className="px-4 py-4 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedKeys.has(key.id)}
                        onChange={() => toggleKeySelection(key.id)}
                        className="rounded border-gray-300"
                      />
                    </td>

                    {/* Key and Category */}
                    <td className="px-4 py-4 text-sm">
                      <div className="font-medium text-gray-900 mb-1">{key.key}</div>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {key.category || "uncategorized"}
                      </span>
                    </td>

                    {/* English (Default) */}
                    <td className="px-4 py-4 text-sm text-gray-900">
                      <div className="max-w-xs">
                        <textarea
                          value={key.defaultContent}
                          readOnly
                          className="w-full p-2 text-sm border border-gray-200 rounded bg-gray-50 resize-none"
                          rows={Math.min(4, Math.ceil(key.defaultContent.length / 50))}
                        />
                      </div>
                    </td>

                    {/* French */}
                    <td className="px-4 py-4 text-sm">
                      <div className="max-w-xs">
                        {frTranslation ? (
                          <div className="space-y-2">
                            <textarea
                              value={editingStates[frTranslation.id] ?? frTranslation.content}
                              onChange={(e) => updateTranslationContent(frTranslation.id, e.target.value)}
                              className="w-full p-2 text-sm border border-gray-300 rounded resize-none"
                              rows={Math.min(4, Math.ceil((editingStates[frTranslation.id] ?? frTranslation.content).length / 50))}
                            />
                            <div className="flex items-center gap-2">
                              {frTranslation.isAutoTranslated === "true" && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  AI
                                </span>
                              )}
                              <button
                                onClick={() => autoTranslate(key.id, key.defaultContent, "fr")}
                                className="text-xs text-blue-600 hover:text-blue-800"
                              >
                                🤖 Re-translate
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <div className="p-2 text-xs text-gray-500 border border-dashed border-gray-300 rounded bg-gray-50">
                              No translation
                            </div>
                            <button
                              onClick={() => autoTranslate(key.id, key.defaultContent, "fr")}
                              className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                            >
                              🤖 Auto Translate
                            </button>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Dutch */}
                    <td className="px-4 py-4 text-sm">
                      <div className="max-w-xs">
                        {nlTranslation ? (
                          <div className="space-y-2">
                            <textarea
                              value={editingStates[nlTranslation.id] ?? nlTranslation.content}
                              onChange={(e) => updateTranslationContent(nlTranslation.id, e.target.value)}
                              className="w-full p-2 text-sm border border-gray-300 rounded resize-none"
                              rows={Math.min(4, Math.ceil((editingStates[nlTranslation.id] ?? nlTranslation.content).length / 50))}
                            />
                            <div className="flex items-center gap-2">
                              {nlTranslation.isAutoTranslated === "true" && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  AI
                                </span>
                              )}
                              <button
                                onClick={() => autoTranslate(key.id, key.defaultContent, "nl")}
                                className="text-xs text-blue-600 hover:text-blue-800"
                              >
                                🤖 Re-translate
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <div className="p-2 text-xs text-gray-500 border border-dashed border-gray-300 rounded bg-gray-50">
                              No translation
                            </div>
                            <button
                              onClick={() => autoTranslate(key.id, key.defaultContent, "nl")}
                              className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                            >
                              🤖 Auto Translate
                            </button>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Key Modal */}
      {showCreateKeyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add Translation Key</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Key
                </label>
                <input
                  type="text"
                  value={newKey.key}
                  onChange={(e) => setNewKey({ ...newKey, key: e.target.value })}
                  placeholder="e.g., hero.title"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={newKey.category}
                  onChange={(e) => setNewKey({ ...newKey, category: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="">Select category</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={newKey.description}
                  onChange={(e) => setNewKey({ ...newKey, description: e.target.value })}
                  placeholder="Brief description of this content"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Default Content (English)
                </label>
                <textarea
                  value={newKey.defaultContent}
                  onChange={(e) => setNewKey({ ...newKey, defaultContent: e.target.value })}
                  placeholder="Enter the English content"
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowCreateKeyModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={createTranslationKey}
                disabled={!newKey.key || !newKey.defaultContent}
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90 disabled:opacity-50"
              >
                Create Key
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Translation Modal */}
      {showTranslateModal && selectedKey && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Translate: {selectedKey.key}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Original (English)
                </label>
                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm">
                  {selectedKey.defaultContent}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Translation ({selectedLanguage === "fr" ? "French" : "Dutch"})
                </label>
                <textarea
                  value={translationForm.content}
                  onChange={(e) => setTranslationForm({ ...translationForm, content: e.target.value })}
                  placeholder={`Enter ${selectedLanguage === "fr" ? "French" : "Dutch"} translation`}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Context (Optional)
                </label>
                <input
                  type="text"
                  value={translationForm.context}
                  onChange={(e) => setTranslationForm({ ...translationForm, context: e.target.value })}
                  placeholder="Additional context for this translation"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowTranslateModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={() => autoTranslate(selectedKey.id, selectedKey.defaultContent, selectedLanguage)}
                className="px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-md hover:bg-yellow-700"
              >
                Auto Translate
              </button>
              <button
                onClick={() => createTranslation(selectedKey.id, selectedLanguage, translationForm.content)}
                disabled={!translationForm.content}
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90 disabled:opacity-50"
              >
                Save Translation
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}

export default withAdminAuth(TranslationManagement);
