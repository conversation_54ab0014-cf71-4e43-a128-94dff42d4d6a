import { Link } from "wouter";
import { motion } from "framer-motion";
import { FileText, Plus, Edit, Trash2, LogOut, User } from "lucide-react";
import { withAdminAuth, useAdminAuth } from "@/hooks/useAdminAuth";

function BlogManagement() {
  const { user, logout } = useAdminAuth();
  // Placeholder blog posts (in real implementation, these would come from database)
  const blogPosts = [
    {
      id: "1",
      title: "5 Essential Legal Research Techniques Every Solo Practitioner Should Master",
      status: "published",
      publishDate: "May 20, 2025",
      author: "AiLex Team",
      category: "Research Tips",
    },
    {
      id: "2", 
      title: "How AI is Transforming Small Law Firm Operations",
      status: "draft",
      publishDate: "Draft",
      author: "AiLex Team",
      category: "Technology",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Blog Management</h1>
                <p className="mt-2 text-gray-600">Create and manage blog articles</p>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <User className="w-4 h-4" />
                  <span>{user?.name}</span>
                </div>
                <Link
                  href="/admin"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  ← Back to Admin
                </Link>
                <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  New Article
                </button>
                <button
                  onClick={logout}
                  className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Coming Soon Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-6 mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <FileText className="h-6 w-6 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-800">
                Blog Management Coming Soon
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  The blog management system is currently under development. For now, blog posts are 
                  statically defined in the code. This interface will be fully functional in a future update.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Blog Posts Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Current Blog Posts</h2>
            <p className="text-sm text-gray-500 mt-1">These are currently hardcoded in the application</p>
          </div>
          
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Publish Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {blogPosts.map((post) => (
                <tr key={post.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm">
                    <div className="font-medium text-gray-900">{post.title}</div>
                    <div className="text-gray-500">by {post.author}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {post.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      post.status === "published" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-yellow-100 text-yellow-800"
                    }`}>
                      {post.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {post.publishDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900 disabled:opacity-50" disabled>
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-900 disabled:opacity-50" disabled>
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Features Coming Soon */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Planned Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">Rich Text Editor</h4>
              <p className="text-sm text-gray-500">WYSIWYG editor for creating and editing blog posts</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">Media Management</h4>
              <p className="text-sm text-gray-500">Upload and manage images for blog posts</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">SEO Optimization</h4>
              <p className="text-sm text-gray-500">Meta tags, descriptions, and SEO preview</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">Draft Management</h4>
              <p className="text-sm text-gray-500">Save drafts and schedule publishing</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">Categories & Tags</h4>
              <p className="text-sm text-gray-500">Organize posts with categories and tags</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-2">Multi-Language</h4>
              <p className="text-sm text-gray-500">Create blog posts in multiple languages</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAdminAuth(BlogManagement);
