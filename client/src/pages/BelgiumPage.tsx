import { useEffect } from "react";
import { useRoute } from "wouter";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SocialProof from "@/components/SocialProof";
import ProblemSolution from "@/components/ProblemSolution";
import FeatureTrio from "@/components/FeatureTrio";
import RoiCalculator from "@/components/RoiCalculator";
import Testimonials from "@/components/Testimonials";
import Pricing from "@/components/Pricing";
import SecurityBanner from "@/components/SecurityBanner";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";
import { updateMetaTags } from "@/lib/seo";
import { useTranslation } from "@/hooks/useTranslation";
import type { Country, Language } from "@/types";

type BelgiumPageProps = {
  country?: Country;
};

// Belgian content now managed through translation system
// See translation keys: hero.title_belgian, hero.subtitle_belgian

// Belgian SEO data
const belgiumSEO = {
  en: {
    title: "AI Legal Assistant for Belgian Lawyers | AiLex",
    description: "Transform your Belgian law practice with AiLex. Automate client intake, legal research, and document drafting. Built for solo advocates and small law firms in Belgium. Start your free trial.",
    keywords: "Belgian legal assistant, AI lawyer Belgium, legal technology Belgium, solo advocate Belgium, small law firm Belgium, Belgian civil law",
    ogTitle: "AiLex - AI Legal Assistant for Belgian Lawyers",
    ogDescription: "AI-powered legal assistant specifically designed for Belgian advocates and law firms.",
    canonical: "https://ailexlaw.com/be",
  },
  fr: {
    title: "Assistant Juridique IA pour Avocats Belges | AiLex",
    description: "Transformez votre cabinet d'avocat belge avec AiLex. Automatisez l'accueil client, la recherche juridique et la rédaction de documents. Conçu pour les avocats indépendants et petits cabinets en Belgique.",
    keywords: "assistant juridique belge, avocat IA Belgique, technologie juridique Belgique, avocat indépendant Belgique, petit cabinet d'avocat Belgique",
    ogTitle: "AiLex - Assistant Juridique IA pour Avocats Belges",
    ogDescription: "Assistant juridique alimenté par l'IA spécialement conçu pour les avocats et cabinets belges.",
    canonical: "https://ailexlaw.com/be/fr",
  },
  nl: {
    title: "AI Juridische Assistent voor Belgische Advocaten | AiLex",
    description: "Transformeer uw Belgische advocatenpraktijk met AiLex. Automatiseer cliëntintake, juridisch onderzoek en documentopstelling. Gebouwd voor solo-advocaten en kleine advocatenkantoren in België.",
    keywords: "Belgische juridische assistent, AI advocaat België, juridische technologie België, solo advocaat België, klein advocatenkantoor België",
    ogTitle: "AiLex - AI Juridische Assistent voor Belgische Advocaten",
    ogDescription: "AI-aangedreven juridische assistent speciaal ontworpen voor Belgische advocaten en advocatenkantoren.",
    canonical: "https://ailexlaw.com/be/nl",
  },
};

export default function BelgiumPage({ country = "BE" }: BelgiumPageProps) {
  const [, params] = useRoute("/be/:language");
  const language = (params?.language as Language) || "en";
  const { t } = useTranslation();

  // Get Belgian-specific content using translation system
  const heroTitle = t('hero.title_belgian');
  const heroSubtitle = t('hero.subtitle_belgian');
  const seoTitle = t('seo.title_belgian');
  const seoDescription = t('seo.description_belgian');

  // Set SEO meta tags based on language
  useEffect(() => {
    updateMetaTags({
      title: seoTitle,
      description: seoDescription,
      keywords: belgiumSEO[language]?.keywords || '',
      ogTitle: seoTitle,
      ogDescription: seoDescription,
      canonical: `https://ailexlaw.com/be${language !== 'en' ? `/${language}` : ''}`
    });
  }, [language, seoTitle, seoDescription]);

  return (
    <div className="relative overflow-x-hidden">
      <Navbar currentCountry={country} currentLanguage={language} />
      <main>
        <Hero
          customTitle={heroTitle}
          customSubtitle={heroSubtitle}
          country={country}
          language={language}
        />
        <SocialProof />
        <ProblemSolution />
        <FeatureTrio country={country} language={language} />
        <RoiCalculator currency="EUR" />
        <Testimonials defaultCountry={country} defaultLanguage={language} />
        <Pricing currency="EUR" />
        <SecurityBanner />
      </main>
      <Footer />
      <CookieNotice />

      {/* Mobile action button */}
      <div className="fixed md:hidden bottom-6 right-6 z-40">
        <button className="bg-primary text-white h-14 w-14 rounded-full shadow-lg flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        </button>
      </div>

      {/* Language selector for Belgium */}
      <div className="fixed top-20 right-4 z-30 md:block hidden">
        <div className="bg-white rounded-lg shadow-md p-2">
          <select
            value={language}
            onChange={(e) => {
              const newLang = e.target.value;
              window.location.href = newLang === "en" ? "/be" : `/be/${newLang}`;
            }}
            className="text-sm font-medium outline-none"
          >
            <option value="en">English</option>
            <option value="fr">Français</option>
            <option value="nl">Nederlands</option>
          </select>
        </div>
      </div>
    </div>
  );
}
