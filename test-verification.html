<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Verification</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Email Verification Test</h1>
    
    <div class="test-section">
        <h3>Test with Valid <PERSON>ken</h3>
        <button onclick="testValidToken()">Test Valid Token</button>
        <div id="valid-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test with Invalid Token</h3>
        <button onclick="testInvalidToken()">Test Invalid Token</button>
        <div id="invalid-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5173';
        
        async function testValidToken() {
            const resultDiv = document.getElementById('valid-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/prospects/verify-email`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: '6LEY6isUMOs9ljwrX5DTin4l-RBnhNCV' // Valid token from database
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h4>Response (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }
        
        async function testInvalidToken() {
            const resultDiv = document.getElementById('invalid-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/prospects/verify-email`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: 'invalid-token-12345'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h4>Response (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
