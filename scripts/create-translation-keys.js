/**
 * <PERSON><PERSON><PERSON> to create all translation keys for AiLex website
 * Run with: node scripts/create-translation-keys.js
 */

const translationKeys = [
  // Hero Section
  {
    key: "hero.title",
    category: "hero",
    description: "Main hero title",
    defaultContent: "Your AI Legal Associate—Works While You're in Court."
  },
  {
    key: "hero.subtitle",
    category: "hero", 
    description: "Hero subtitle describing the service",
    defaultContent: "Research like a librarian, draft like an associate, organize like a paralegal — at solo-firm prices."
  },
  {
    key: "hero.tagline",
    category: "hero",
    description: "Hero tagline emphasizing capability",
    defaultContent: "AiLex can do it ALL for you."
  },
  {
    key: "hero.cta_primary",
    category: "hero",
    description: "Primary call-to-action button",
    defaultContent: "Get Started Free"
  },
  {
    key: "hero.cta_secondary", 
    category: "hero",
    description: "Secondary call-to-action button",
    defaultContent: "Watch Demo"
  },

  // Navigation
  {
    key: "nav.features",
    category: "navigation",
    description: "Features navigation link",
    defaultContent: "Features"
  },
  {
    key: "nav.pricing",
    category: "navigation", 
    description: "Pricing navigation link",
    defaultContent: "Pricing"
  },
  {
    key: "nav.contact",
    category: "navigation",
    description: "Contact navigation link", 
    defaultContent: "Contact"
  },
  {
    key: "nav.blog",
    category: "navigation",
    description: "Blog navigation link",
    defaultContent: "Blog"
  },

  // Problem/Solution Section
  {
    key: "problems.admin_time.title",
    category: "problems",
    description: "Problem about admin time",
    defaultContent: "40% lawyer time is unbilled admin."
  },
  {
    key: "problems.admin_time.description", 
    category: "problems",
    description: "Description of admin time problem",
    defaultContent: "The average solo attorney loses nearly half their potential billable hours to administrative tasks."
  },
  {
    key: "problems.missed_calls.title",
    category: "problems", 
    description: "Problem about missed calls",
    defaultContent: "Missed intake calls cost solos $7k+/yr."
  },
  {
    key: "problems.missed_calls.description",
    category: "problems",
    description: "Description of missed calls problem", 
    defaultContent: "Every missed call is a potential client gone. Small firms without reception staff lose thousands annually."
  },
  {
    key: "problems.expensive_software.title",
    category: "problems",
    description: "Problem about expensive software",
    defaultContent: "Big-law software starts at $199/user."
  },
  {
    key: "problems.expensive_software.description",
    category: "problems",
    description: "Description of expensive software problem",
    defaultContent: "Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners."
  },

  // Features Section
  {
    key: "features.section_title",
    category: "features",
    description: "Features section main title",
    defaultContent: "What AiLex Does for Solo Lawyers & Small Firms"
  },
  {
    key: "features.section_badge",
    category: "features", 
    description: "Features section badge text",
    defaultContent: "Core Features"
  },
  {
    key: "features.research.title",
    category: "features",
    description: "Research feature title",
    defaultContent: "Research Module"
  },
  {
    key: "features.research.description",
    category: "features",
    description: "Research feature description", 
    defaultContent: "AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds."
  },
  {
    key: "features.intake.title",
    category: "features",
    description: "Intake feature title",
    defaultContent: "Intake Module"
  },
  {
    key: "features.intake.description",
    category: "features",
    description: "Intake feature description",
    defaultContent: "AiLex answers every call, captures client info (name, case type, urgency), and files everything into your dashboard automatically."
  },
  {
    key: "features.organization.title", 
    category: "features",
    description: "Organization feature title",
    defaultContent: "Organization Module"
  },
  {
    key: "features.organization.description",
    category: "features",
    description: "Organization feature description",
    defaultContent: "AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most."
  },

  // Pricing Section
  {
    key: "pricing.section_title",
    category: "pricing",
    description: "Pricing section title",
    defaultContent: "Choose Your Plan"
  },
  {
    key: "pricing.section_subtitle",
    category: "pricing", 
    description: "Pricing section subtitle",
    defaultContent: "Big-Law Tech, Solo-Firm Pricing"
  },
  {
    key: "pricing.billing_monthly",
    category: "pricing",
    description: "Monthly billing option",
    defaultContent: "Monthly"
  },
  {
    key: "pricing.billing_annual",
    category: "pricing",
    description: "Annual billing option", 
    defaultContent: "Annual"
  },
  {
    key: "pricing.billing_save",
    category: "pricing",
    description: "Annual billing savings text",
    defaultContent: "Save 15%"
  },
  {
    key: "pricing.solo.name",
    category: "pricing",
    description: "Solo plan name",
    defaultContent: "Solo"
  },
  {
    key: "pricing.solo.description",
    category: "pricing",
    description: "Solo plan description",
    defaultContent: "Perfect for solo practitioners"
  },
  {
    key: "pricing.solo.features.users",
    category: "pricing",
    description: "Solo plan user count",
    defaultContent: "Includes 2 users"
  },
  {
    key: "pricing.solo.features.practice",
    category: "pricing", 
    description: "Solo plan practice areas",
    defaultContent: "Choose 1 legal practice: PI, Family, or Criminal"
  },
  {
    key: "pricing.team.name",
    category: "pricing",
    description: "Team plan name", 
    defaultContent: "Team"
  },
  {
    key: "pricing.team.description",
    category: "pricing",
    description: "Team plan description",
    defaultContent: "For small law firms"
  },
  {
    key: "pricing.team.features.users",
    category: "pricing",
    description: "Team plan user count",
    defaultContent: "Includes 5 users"
  },
  {
    key: "pricing.team.features.practice",
    category: "pricing",
    description: "Team plan practice areas",
    defaultContent: "All legal practices included (PI, Family, Criminal)"
  },
  {
    key: "pricing.scale.name",
    category: "pricing",
    description: "Scale plan name",
    defaultContent: "Scale"
  },
  {
    key: "pricing.scale.description", 
    category: "pricing",
    description: "Scale plan description",
    defaultContent: "For growing firms"
  },
  {
    key: "pricing.scale.features.users",
    category: "pricing",
    description: "Scale plan user count",
    defaultContent: "Includes 10 users"
  },
  {
    key: "pricing.scale.features.practice",
    category: "pricing",
    description: "Scale plan practice areas", 
    defaultContent: "All legal practices + premium features"
  },
  {
    key: "pricing.cta_button",
    category: "pricing",
    description: "Pricing CTA button text",
    defaultContent: "Get Started"
  },

  // Footer
  {
    key: "footer.company.about",
    category: "footer",
    description: "Footer about text",
    defaultContent: "AiLex is the AI legal assistant designed specifically for solo practitioners and small law firms."
  },
  {
    key: "footer.links.privacy",
    category: "footer",
    description: "Privacy policy link",
    defaultContent: "Privacy Policy"
  },
  {
    key: "footer.links.terms",
    category: "footer", 
    description: "Terms of service link",
    defaultContent: "Terms of Service"
  },
  {
    key: "footer.links.security",
    category: "footer",
    description: "Security whitepaper link",
    defaultContent: "Security Whitepaper"
  },
  {
    key: "footer.copyright",
    category: "footer",
    description: "Copyright text",
    defaultContent: "© 2025 AiLex. All rights reserved."
  },

  // Contact Form
  {
    key: "contact.title",
    category: "contact",
    description: "Contact form title",
    defaultContent: "Get in Touch"
  },
  {
    key: "contact.description",
    category: "contact",
    description: "Contact form description", 
    defaultContent: "Ready to transform your legal practice? Let's talk."
  },
  {
    key: "contact.form.name",
    category: "contact",
    description: "Name field label",
    defaultContent: "Full Name"
  },
  {
    key: "contact.form.email",
    category: "contact",
    description: "Email field label",
    defaultContent: "Email Address"
  },
  {
    key: "contact.form.message",
    category: "contact",
    description: "Message field label",
    defaultContent: "Message"
  },
  {
    key: "contact.form.submit",
    category: "contact",
    description: "Submit button text",
    defaultContent: "Send Message"
  },

  // Belgium-specific content
  {
    key: "hero.title.belgium",
    category: "hero",
    description: "Belgium-specific hero title",
    defaultContent: "Your AI Legal Associate for Belgian Law—Works While You're in Court."
  },
  {
    key: "hero.subtitle.belgium", 
    category: "hero",
    description: "Belgium-specific hero subtitle",
    defaultContent: "Belgian-specific research, drafting, and practice management tools designed for solo advocates and small law firms."
  },
  {
    key: "features.legal_system.belgium",
    category: "features",
    description: "Belgian legal system reference",
    defaultContent: "Belgian Civil Code Art. 2262bis - Five-year limitation period"
  },
  {
    key: "features.court_system.belgium",
    category: "features", 
    description: "Belgian court system reference",
    defaultContent: "Found 23 similar cases in Belgian courts"
  },
  {
    key: "features.case_law.belgium",
    category: "features",
    description: "Belgian case law reference",
    defaultContent: "Cour de Cassation, 15 mars 2023 - Responsabilité civile appliquée"
  },

  // Common UI Elements
  {
    key: "ui.loading",
    category: "ui",
    description: "Loading text",
    defaultContent: "Loading..."
  },
  {
    key: "ui.error",
    category: "ui",
    description: "Generic error message",
    defaultContent: "An error occurred. Please try again."
  },
  {
    key: "ui.success",
    category: "ui", 
    description: "Generic success message",
    defaultContent: "Success!"
  },
  {
    key: "ui.cancel",
    category: "ui",
    description: "Cancel button text",
    defaultContent: "Cancel"
  },
  {
    key: "ui.save",
    category: "ui",
    description: "Save button text",
    defaultContent: "Save"
  },
  {
    key: "ui.edit",
    category: "ui",
    description: "Edit button text", 
    defaultContent: "Edit"
  },
  {
    key: "ui.delete",
    category: "ui",
    description: "Delete button text",
    defaultContent: "Delete"
  }
];

async function createTranslationKeys() {
  console.log('🚀 Creating translation keys for AiLex...');
  
  const baseUrl = process.env.VERCEL_URL 
    ? `https://${process.env.VERCEL_URL}` 
    : 'http://localhost:3000';
  
  let created = 0;
  let skipped = 0;
  
  for (const keyData of translationKeys) {
    try {
      const response = await fetch(`${baseUrl}/api/translations/keys`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(keyData),
      });
      
      if (response.ok) {
        created++;
        console.log(`✅ Created: ${keyData.key}`);
      } else if (response.status === 409) {
        skipped++;
        console.log(`⏭️  Skipped (exists): ${keyData.key}`);
      } else {
        const error = await response.json();
        console.log(`❌ Failed: ${keyData.key} - ${error.error}`);
      }
    } catch (error) {
      console.log(`❌ Network error for: ${keyData.key} - ${error.message}`);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Created: ${created}`);
  console.log(`   Skipped: ${skipped}`);
  console.log(`   Total: ${translationKeys.length}`);
  console.log(`\n🎉 Translation keys setup complete!`);
  console.log(`\n🔗 Visit your admin panel: ${baseUrl}/admin/translations`);
}

// Run the script
createTranslationKeys().catch(console.error);
