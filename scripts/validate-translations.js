#!/usr/bin/env node

/**
 * Translation Validation Script
 * 
 * Validates translation keys, checks for missing translations,
 * ensures consistency, and validates translation usage in code.
 * 
 * Usage: npm run validate-translations
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// Supabase configuration
const SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc3OTYyNDYsImV4cCI6MjA1MzM3MjI0Nn0._mrkLcgDRn-ejKwHGEC49L2C4SVvNdBcLfJbaFwpTkU';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Validation results
const results = {
  errors: [],
  warnings: [],
  info: [],
  stats: {
    totalKeys: 0,
    totalTranslations: 0,
    missingTranslations: 0,
    unusedKeys: 0,
    invalidUsage: 0
  }
};

/**
 * Fetch all translation data from database
 */
async function fetchTranslationData() {
  console.log('🔄 Fetching translation data from database...');
  
  // Get all translation keys
  const { data: keys, error: keysError } = await supabase
    .from('translation_keys')
    .select(`
      id,
      key,
      default_content,
      content_type,
      show_in_countries,
      category_id,
      translation_categories(name)
    `);

  if (keysError) {
    throw new Error(`Failed to fetch translation keys: ${keysError.message}`);
  }

  // Get all translations
  const { data: translations, error: translationsError } = await supabase
    .from('translations')
    .select('key_id, language, content');

  if (translationsError) {
    throw new Error(`Failed to fetch translations: ${translationsError.message}`);
  }

  results.stats.totalKeys = keys.length;
  results.stats.totalTranslations = translations.length;

  return { keys, translations };
}

/**
 * Validate translation coverage
 */
function validateTranslationCoverage(keys, translations) {
  console.log('🔍 Validating translation coverage...');
  
  const translationMap = new Map();
  translations.forEach(t => {
    if (!translationMap.has(t.key_id)) {
      translationMap.set(t.key_id, new Set());
    }
    translationMap.get(t.key_id).add(t.language);
  });

  const requiredLanguages = ['fr', 'nl']; // French and Dutch for Belgium
  
  keys.forEach(key => {
    const keyTranslations = translationMap.get(key.id) || new Set();
    
    // Check for missing translations
    requiredLanguages.forEach(lang => {
      if (!keyTranslations.has(lang)) {
        if (key.content_type === 'belgium-only' || key.content_type === 'shared') {
          results.warnings.push({
            type: 'missing_translation',
            message: `Missing ${lang} translation for key: ${key.key}`,
            key: key.key,
            language: lang,
            severity: key.content_type === 'belgium-only' ? 'high' : 'medium'
          });
          results.stats.missingTranslations++;
        }
      }
    });

    // Check for empty translations
    translations
      .filter(t => t.key_id === key.id)
      .forEach(translation => {
        if (!translation.content || translation.content.trim().length === 0) {
          results.errors.push({
            type: 'empty_translation',
            message: `Empty translation content for key: ${key.key} (${translation.language})`,
            key: key.key,
            language: translation.language
          });
        }
      });
  });
}

/**
 * Scan code files for translation usage
 */
async function scanCodeForTranslationUsage() {
  console.log('🔍 Scanning code for translation usage...');
  
  const codeFiles = await glob('client/src/**/*.{ts,tsx}', { 
    cwd: process.cwd(),
    ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
  });

  const usedKeys = new Set();
  const invalidUsage = [];

  for (const file of codeFiles) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Find t() function calls - more specific regex
    const tCallRegex = /\bt\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    let match;
    
    while ((match = tCallRegex.exec(content)) !== null) {
      const key = match[1];
      usedKeys.add(key);
      
      // Validate key format
      if (!/^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$/.test(key)) {
        invalidUsage.push({
          file: file.replace(process.cwd() + '/', ''),
          key,
          line: content.substring(0, match.index).split('\n').length,
          issue: 'Invalid key format'
        });
        results.stats.invalidUsage++;
      }
    }

    // Find hardcoded strings that should use translations (more conservative)
    const hardcodedRegex = />\s*([A-Z][^<>{]*[a-z][^<>{}]{8,})\s*</g;
    while ((match = hardcodedRegex.exec(content)) !== null) {
      const text = match[1].trim();
      // Skip if it's likely not user-facing text
      if (text.length > 15 &&
          !text.includes('className') &&
          !text.includes('http') &&
          !text.includes('console') &&
          !text.includes('import') &&
          !text.includes('export') &&
          !text.includes('function') &&
          !text.includes('const ') &&
          !text.includes('let ') &&
          !text.includes('var ') &&
          !/^[A-Z_][A-Z0-9_]*$/.test(text) && // Skip constants
          !/\d{2,}/.test(text) && // Skip strings with many numbers
          !file.includes('test') && // Skip test files
          !file.includes('example')) { // Skip example files
        results.warnings.push({
          type: 'potential_hardcoded_string',
          message: `Potential hardcoded string in ${file.replace(process.cwd() + '/', '')}: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
          file: file.replace(process.cwd() + '/', ''),
          text,
          line: content.substring(0, match.index).split('\n').length
        });
      }
    }
  }

  invalidUsage.forEach(usage => {
    results.errors.push({
      type: 'invalid_key_usage',
      message: `Invalid translation key usage in ${usage.file}:${usage.line} - "${usage.key}"`,
      file: usage.file,
      key: usage.key,
      line: usage.line,
      issue: usage.issue
    });
  });

  return usedKeys;
}

/**
 * Find unused translation keys
 */
function findUnusedKeys(keys, usedKeys) {
  console.log('🔍 Finding unused translation keys...');
  
  keys.forEach(key => {
    if (!usedKeys.has(key.key)) {
      results.warnings.push({
        type: 'unused_key',
        message: `Unused translation key: ${key.key}`,
        key: key.key,
        category: key.translation_categories?.name
      });
      results.stats.unusedKeys++;
    }
  });
}

/**
 * Validate key naming conventions
 */
function validateKeyNamingConventions(keys) {
  console.log('🔍 Validating key naming conventions...');
  
  keys.forEach(key => {
    const keyParts = key.key.split('.');
    
    // Check depth
    if (keyParts.length > 4) {
      results.warnings.push({
        type: 'deep_key_nesting',
        message: `Key has deep nesting (${keyParts.length} levels): ${key.key}`,
        key: key.key
      });
    }
    
    // Check for inconsistent naming
    keyParts.forEach((part, index) => {
      if (part.includes('_') && part.includes('-')) {
        results.warnings.push({
          type: 'inconsistent_naming',
          message: `Key part uses both underscores and hyphens: ${key.key}`,
          key: key.key
        });
      }
      
      if (part.length > 30) {
        results.warnings.push({
          type: 'long_key_part',
          message: `Key part is very long: ${part} in ${key.key}`,
          key: key.key
        });
      }
    });
  });
}

/**
 * Generate validation report
 */
function generateReport() {
  console.log('\n📊 Translation Validation Report');
  console.log('================================');
  
  // Stats
  console.log('\n📈 Statistics:');
  console.log(`  Total translation keys: ${results.stats.totalKeys}`);
  console.log(`  Total translations: ${results.stats.totalTranslations}`);
  console.log(`  Missing translations: ${results.stats.missingTranslations}`);
  console.log(`  Unused keys: ${results.stats.unusedKeys}`);
  console.log(`  Invalid usage: ${results.stats.invalidUsage}`);
  
  // Errors
  if (results.errors.length > 0) {
    console.log(`\n❌ Errors (${results.errors.length}):`);
    results.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error.message}`);
    });
  }
  
  // Warnings
  if (results.warnings.length > 0) {
    console.log(`\n⚠️  Warnings (${results.warnings.length}):`);
    results.warnings.slice(0, 10).forEach((warning, index) => {
      console.log(`  ${index + 1}. ${warning.message}`);
    });
    if (results.warnings.length > 10) {
      console.log(`  ... and ${results.warnings.length - 10} more warnings`);
    }
  }
  
  // Summary
  console.log('\n🎯 Summary:');
  if (results.errors.length === 0) {
    console.log('  ✅ No critical errors found');
  } else {
    console.log(`  ❌ ${results.errors.length} critical errors need attention`);
  }
  
  if (results.warnings.length === 0) {
    console.log('  ✅ No warnings');
  } else {
    console.log(`  ⚠️  ${results.warnings.length} warnings to review`);
  }
  
  // Exit code
  const exitCode = results.errors.length > 0 ? 1 : 0;
  console.log(`\n${exitCode === 0 ? '✅' : '❌'} Validation ${exitCode === 0 ? 'passed' : 'failed'}`);
  
  return exitCode;
}

/**
 * Check if we're in CI environment
 */
function isCI() {
  return process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true';
}

/**
 * Main validation function
 */
async function validateTranslations() {
  try {
    console.log('🔄 Starting translation validation...\n');

    // Fetch data
    const { keys, translations } = await fetchTranslationData();

    // Run validations
    validateTranslationCoverage(keys, translations);
    validateKeyNamingConventions(keys);

    const usedKeys = await scanCodeForTranslationUsage();
    findUnusedKeys(keys, usedKeys);

    // Generate report
    const exitCode = generateReport();

    // In CI, be more strict
    if (isCI() && (results.errors.length > 0 || results.stats.missingTranslations > 10)) {
      console.log('\n🚨 CI Mode: Failing due to translation issues');
      process.exit(1);
    }

    // In development, only fail on critical errors
    if (!isCI() && results.errors.length > 0) {
      console.log('\n⚠️  Development Mode: Warnings only, not failing build');
      process.exit(0);
    }

    process.exit(exitCode);

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Run validation
validateTranslations();
