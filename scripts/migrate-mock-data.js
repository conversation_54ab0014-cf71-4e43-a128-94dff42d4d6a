import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || 'https://anwefmklplkjxkmzpnva.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY is required for migration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Mock translation keys data from dev-server.js
const mockTranslationKeys = [
  // Shared content - shows in both US and Belgium
  {
    key: 'hero.title',
    category: 'hero',
    description: 'Main hero title',
    defaultContent: 'Your AI Legal Assistant for Modern Law Practice',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared',
    metadata: {
      styling: {
        belgiumClassName: 'text-blue-800 font-bold',
        usClassName: 'text-gray-900 font-bold'
      }
    }
  },
  {
    key: 'hero.subtitle',
    category: 'hero',
    description: 'Main hero subtitle',
    defaultContent: 'AI-powered legal research, drafting, and practice management tools designed for solo practitioners and small law firms.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'hero.tagline',
    category: 'hero',
    description: 'Hero tagline emphasizing capability',
    defaultContent: 'AiLex can do it ALL for you.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'hero.cta_primary',
    category: 'hero',
    description: 'Primary call-to-action button',
    defaultContent: 'Get Started Free',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'hero.cta_secondary',
    category: 'hero',
    description: 'Secondary call-to-action button',
    defaultContent: 'Watch Demo',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },

  // Belgium-only content
  {
    key: 'legal.belgian_certification',
    category: 'legal',
    description: 'Belgian bar certification notice',
    defaultContent: 'Certified by the Belgian Bar Association for legal technology compliance.',
    showInCountries: ['BE'],
    hideInCountries: [],
    contentType: 'belgium-only'
  },

  // US-only content
  {
    key: 'legal.us_bar_admission',
    category: 'legal',
    description: 'US bar admission notice',
    defaultContent: 'Licensed to practice in Texas, Florida, and New York.',
    showInCountries: ['US'],
    hideInCountries: [],
    contentType: 'us-only'
  },

  // Test content with variables
  {
    key: 'welcome.message',
    category: 'general',
    description: 'Welcome message with variables',
    defaultContent: 'Welcome {{name}} to {{company}}! We\'re excited to help your practice grow.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },

  // Problem Solution Section
  {
    key: 'problems.admin_time.title',
    category: 'problems',
    description: 'Admin time problem title',
    defaultContent: '40% lawyer time is unbilled admin.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'problems.admin_time.description',
    category: 'problems',
    description: 'Admin time problem description',
    defaultContent: 'The average solo attorney loses nearly half their potential billable hours to administrative tasks.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'problems.missed_calls.title',
    category: 'problems',
    description: 'Missed calls problem title',
    defaultContent: 'Missed intake calls cost solos $7k+/yr.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'problems.missed_calls.description',
    category: 'problems',
    description: 'Missed calls problem description',
    defaultContent: 'Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'problems.expensive_software.title',
    category: 'problems',
    description: 'Expensive software problem title',
    defaultContent: 'Big-law software starts at $199/user.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'problems.expensive_software.description',
    category: 'problems',
    description: 'Expensive software problem description',
    defaultContent: 'Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },

  // Solutions
  {
    key: 'solutions.receptionist.title',
    category: 'solutions',
    description: 'AI Receptionist solution title',
    defaultContent: 'Never Miss a Lead — AiLex Answers Every Call',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'solutions.receptionist.description',
    category: 'solutions',
    description: 'AI Receptionist solution description',
    defaultContent: 'AiLex\'s AI Receptionist answers inbound calls instantly, captures the client\'s name, case type, urgency, and files everything into your dashboard.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'solutions.case_management.title',
    category: 'solutions',
    description: 'Case management solution title',
    defaultContent: 'Never Miss a Deadline — AiLex Keeps Every Case on Track',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'solutions.case_management.description',
    category: 'solutions',
    description: 'Case management solution description',
    defaultContent: 'AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'solutions.document_drafting.title',
    category: 'solutions',
    description: 'Document drafting solution title',
    defaultContent: 'Never Redraft a Motion — AiLex Writes It for You',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'solutions.document_drafting.description',
    category: 'solutions',
    description: 'Document drafting solution description',
    defaultContent: 'AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior associate, minus the overhead.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },

  // Features Section
  {
    key: 'features.title',
    category: 'features',
    description: 'Features section title',
    defaultContent: 'What AiLex Does for Solo Lawyers & Small Firms',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.research.title',
    category: 'features',
    description: 'Research module title',
    defaultContent: 'Research Module',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.research.description',
    category: 'features',
    description: 'Research module description',
    defaultContent: 'AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.drafting.title',
    category: 'features',
    description: 'Drafting module title',
    defaultContent: 'Drafting Module',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.drafting.description',
    category: 'features',
    description: 'Drafting module description',
    defaultContent: 'AiLex writes motions, briefs, and contracts using your firm\'s style and local court rules.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.intake.title',
    category: 'features',
    description: 'Intake module title',
    defaultContent: 'Intake Module',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  },
  {
    key: 'features.intake.description',
    category: 'features',
    description: 'Intake module description',
    defaultContent: 'AiLex answers calls, schedules consultations, and pre-qualifies leads while you focus on billable work.',
    showInCountries: ['US', 'BE'],
    hideInCountries: [],
    contentType: 'shared'
  }
];

// Mock translations data
const mockTranslations = [
  // Dutch translations
  {
    keyName: 'hero.title',
    language: 'nl',
    content: 'Uw AI Juridische Assistent voor Moderne Rechtspraktijk',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  },
  {
    keyName: 'hero.subtitle',
    language: 'nl',
    content: 'AI-aangedreven juridisch onderzoek, concepten en praktijkbeheertools ontworpen voor solo-praktijken en kleine advocatenkantoren.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  },
  {
    keyName: 'legal.belgian_certification',
    language: 'nl',
    content: 'Gecertificeerd door de Belgische Orde van Advocaten voor juridische technologie compliance.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  },

  // French translations
  {
    keyName: 'hero.title',
    language: 'fr',
    content: 'Votre Assistant Juridique IA pour la Pratique Juridique Moderne',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  },
  {
    keyName: 'hero.subtitle',
    language: 'fr',
    content: 'Recherche juridique alimentée par l\'IA, rédaction et outils de gestion de pratique conçus pour les praticiens solo et les petits cabinets d\'avocats.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  },
  {
    keyName: 'legal.belgian_certification',
    language: 'fr',
    content: 'Certifié par l\'Ordre des Avocats Belges pour la conformité technologique juridique.',
    context: '',
    isAutoTranslated: true,
    confidence: 0.95
  }
];

async function migrateData() {
  console.log('🚀 Starting migration of mock data to Supabase...');

  try {
    // Step 1: Get category IDs
    console.log('📂 Fetching categories...');
    const { data: categories, error: categoriesError } = await supabase
      .from('translation_categories')
      .select('id, name');

    if (categoriesError) {
      throw new Error(`Failed to fetch categories: ${categoriesError.message}`);
    }

    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    console.log(`✅ Found ${categories.length} categories`);

    // Step 2: Migrate translation keys
    console.log('🔑 Migrating translation keys...');
    
    for (const mockKey of mockTranslationKeys) {
      const keyData = {
        key: mockKey.key,
        category_id: categoryMap[mockKey.category] || null,
        description: mockKey.description,
        default_content: mockKey.defaultContent,
        show_in_countries: mockKey.showInCountries || ['US', 'BE'],
        hide_in_countries: mockKey.hideInCountries || [],
        content_type: mockKey.contentType || 'shared',
        metadata: mockKey.metadata || {}
      };

      // Check if key already exists
      const { data: existingKey } = await supabase
        .from('translation_keys')
        .select('id')
        .eq('key', mockKey.key)
        .single();

      if (existingKey) {
        console.log(`⏭️  Key '${mockKey.key}' already exists, skipping...`);
        continue;
      }

      const { error: keyError } = await supabase
        .from('translation_keys')
        .insert(keyData);

      if (keyError) {
        console.error(`❌ Failed to insert key '${mockKey.key}':`, keyError.message);
      } else {
        console.log(`✅ Inserted key: ${mockKey.key}`);
      }
    }

    // Step 3: Migrate translations
    console.log('🌐 Migrating translations...');

    // Get all translation keys with their IDs
    const { data: allKeys, error: keysError } = await supabase
      .from('translation_keys')
      .select('id, key');

    if (keysError) {
      throw new Error(`Failed to fetch translation keys: ${keysError.message}`);
    }

    const keyIdMap = {};
    allKeys.forEach(key => {
      keyIdMap[key.key] = key.id;
    });

    for (const mockTranslation of mockTranslations) {
      const keyId = keyIdMap[mockTranslation.keyName];
      
      if (!keyId) {
        console.log(`⚠️  Key '${mockTranslation.keyName}' not found, skipping translation...`);
        continue;
      }

      const translationData = {
        key_id: keyId,
        language: mockTranslation.language,
        content: mockTranslation.content,
        context: mockTranslation.context,
        is_auto_translated: mockTranslation.isAutoTranslated,
        confidence: mockTranslation.confidence
      };

      // Check if translation already exists
      const { data: existingTranslation } = await supabase
        .from('translations')
        .select('id')
        .eq('key_id', keyId)
        .eq('language', mockTranslation.language)
        .single();

      if (existingTranslation) {
        console.log(`⏭️  Translation '${mockTranslation.keyName}' (${mockTranslation.language}) already exists, skipping...`);
        continue;
      }

      const { error: translationError } = await supabase
        .from('translations')
        .insert(translationData);

      if (translationError) {
        console.error(`❌ Failed to insert translation '${mockTranslation.keyName}' (${mockTranslation.language}):`, translationError.message);
      } else {
        console.log(`✅ Inserted translation: ${mockTranslation.keyName} (${mockTranslation.language})`);
      }
    }

    console.log('🎉 Migration completed successfully!');

    // Step 4: Show summary
    const { data: finalKeys } = await supabase
      .from('translation_keys')
      .select('id');
    
    const { data: finalTranslations } = await supabase
      .from('translations')
      .select('id');

    console.log('\n📊 Migration Summary:');
    console.log(`   Translation Keys: ${finalKeys?.length || 0}`);
    console.log(`   Translations: ${finalTranslations?.length || 0}`);

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration
migrateData();
