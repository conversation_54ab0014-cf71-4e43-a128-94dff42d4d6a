import { createClient } from "@supabase/supabase-js";
import crypto from "crypto";

// Load environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error("Missing Supabase configuration");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTestProspect() {
  const testEmail = "<EMAIL>";
  const verificationToken = crypto.randomBytes(32).toString("base64url");
  
  console.log("Creating test prospect...");
  console.log("Email:", testEmail);
  console.log("Verification token:", verificationToken);
  
  // First, delete any existing test prospect
  await supabase
    .from("prospects")
    .delete()
    .eq("email", testEmail);
  
  // Create new test prospect
  const { data, error } = await supabase
    .from("prospects")
    .insert([
      {
        email: testEmail,
        first_name: "Test",
        last_name: "User",
        email_verified: false,
        email_verification_token: verificationToken,
        newsletter_subscribed: true,
        gdpr_consent: true,
        gdpr_consent_date: new Date().toISOString(),
        marketing_consent: true,
        signup_source: "website",
        signup_page: "/test",
        communication_preferences: {
          email: true,
          sms: false,
          phone: false
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ])
    .select();
  
  if (error) {
    console.error("Error creating test prospect:", error);
    process.exit(1);
  }
  
  console.log("Test prospect created successfully!");
  console.log("Test verification URL:");
  console.log(`http://localhost:3001/verify-email?token=${verificationToken}`);
  
  return { email: testEmail, token: verificationToken };
}

createTestProspect().catch(console.error);
