#!/usr/bin/env node

/**
 * CLI tool for creating translation keys
 * 
 * Usage:
 *   npm run create-key "hero.new_title" "Welcome to AiLex" --category=hero
 *   npm run create-key "legal.belgian_notice" "Belgian legal notice" --belgium-only
 *   npm run create-key "features.new_feature" "Amazing new feature" --description="Feature description"
 */

import { createClient } from '@supabase/supabase-js';
import { execSync } from 'child_process';
import readline from 'readline';

// Supabase configuration
const SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzc5NjI0NiwiZXhwIjoyMDUzMzcyMjQ2fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log(`
🔑 Translation Key Creator

Usage:
  npm run create-key <key> <content> [options]

Examples:
  npm run create-key "hero.new_title" "Welcome to AiLex"
  npm run create-key "legal.belgian_notice" "Belgian legal notice" --belgium-only
  npm run create-key "features.new_feature" "Amazing feature" --category=features --description="Feature description"

Options:
  --category=<name>     Specify category (hero, features, legal, etc.)
  --description=<text>  Add description for the key
  --belgium-only        Make this key Belgium-only
  --us-only            Make this key US-only
  --interactive        Interactive mode with prompts
  --help               Show this help message
    `);
    process.exit(1);
  }

  const [key, content, ...options] = args;
  const flags = {};
  
  options.forEach(option => {
    if (option.startsWith('--')) {
      const [name, value] = option.slice(2).split('=');
      flags[name] = value || true;
    }
  });

  return { key, content, flags };
}

// Validate key format
function validateKey(key) {
  const errors = [];
  
  if (!key || key.trim().length === 0) {
    errors.push('Key cannot be empty');
  }
  
  if (!/^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$/.test(key)) {
    errors.push('Key must use lowercase letters, numbers, underscores, and dots only');
  }
  
  if (key.length > 100) {
    errors.push('Key is too long (max 100 characters)');
  }
  
  return errors;
}

// Suggest category based on key
function suggestCategory(key) {
  const [firstPart] = key.split('.');
  
  const categoryMap = {
    'hero': 'hero',
    'features': 'features',
    'problems': 'problems',
    'solutions': 'solutions',
    'legal': 'legal',
    'seo': 'seo',
    'nav': 'general',
    'footer': 'general',
    'form': 'general',
    'button': 'general',
    'error': 'general',
    'success': 'general'
  };
  
  return categoryMap[firstPart] || 'general';
}

// Get or create category
async function ensureCategory(categoryName) {
  // Try to get existing category
  const { data: existingCategory } = await supabase
    .from('translation_categories')
    .select('id')
    .eq('name', categoryName)
    .single();

  if (existingCategory) {
    return existingCategory.id;
  }

  // Create new category
  const { data: newCategory, error } = await supabase
    .from('translation_categories')
    .insert({
      name: categoryName,
      description: `Auto-created category for ${categoryName} content`,
      is_active: true,
      display_order: 999
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create category: ${error.message}`);
  }

  console.log(`✅ Created new category: ${categoryName}`);
  return newCategory.id;
}

// Interactive mode
async function interactiveMode() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));

  try {
    console.log('\n🔑 Interactive Translation Key Creator\n');
    
    const key = await question('Translation key (e.g., hero.new_title): ');
    const content = await question('Default content (English): ');
    const description = await question('Description (optional): ');
    const category = await question(`Category (suggested: ${suggestCategory(key)}): `) || suggestCategory(key);
    const contentType = await question('Content type (shared/belgium-only/us-only) [shared]: ') || 'shared';
    
    rl.close();
    
    return {
      key: key.trim(),
      content: content.trim(),
      flags: {
        description: description.trim() || undefined,
        category: category.trim(),
        [contentType.replace('-', '_')]: contentType !== 'shared'
      }
    };
  } catch (error) {
    rl.close();
    throw error;
  }
}

// Create translation key
async function createTranslationKey(key, content, flags) {
  try {
    console.log(`\n🔄 Creating translation key: ${key}`);
    
    // Validate key
    const errors = validateKey(key);
    if (errors.length > 0) {
      console.error('❌ Validation errors:');
      errors.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }
    
    // Check if key already exists
    const { data: existingKey } = await supabase
      .from('translation_keys')
      .select('id')
      .eq('key', key)
      .single();

    if (existingKey) {
      console.error(`❌ Translation key '${key}' already exists`);
      process.exit(1);
    }
    
    // Determine content type and countries
    let contentType = 'shared';
    let showInCountries = ['US', 'BE'];
    
    if (flags.belgium_only) {
      contentType = 'belgium-only';
      showInCountries = ['BE'];
    } else if (flags.us_only) {
      contentType = 'us-only';
      showInCountries = ['US'];
    }
    
    // Get category
    const categoryName = flags.category || suggestCategory(key);
    const categoryId = await ensureCategory(categoryName);
    
    // Create the translation key
    const { data: newKey, error: createError } = await supabase
      .from('translation_keys')
      .insert({
        key,
        category_id: categoryId,
        description: flags.description || `${categoryName} content: ${key}`,
        default_content: content,
        show_in_countries: showInCountries,
        hide_in_countries: [],
        content_type: contentType,
        metadata: {}
      })
      .select()
      .single();

    if (createError) {
      console.error(`❌ Failed to create translation key: ${createError.message}`);
      process.exit(1);
    }
    
    console.log(`✅ Created translation key: ${key}`);
    console.log(`   Category: ${categoryName}`);
    console.log(`   Content Type: ${contentType}`);
    console.log(`   Countries: ${showInCountries.join(', ')}`);
    
    // Regenerate TypeScript types
    console.log('\n🔄 Regenerating TypeScript types...');
    try {
      execSync('npm run generate-types', { stdio: 'inherit' });
      console.log('✅ TypeScript types updated');
    } catch (error) {
      console.warn('⚠️  Failed to regenerate types - run "npm run generate-types" manually');
    }
    
    console.log(`\n🎉 Translation key '${key}' is ready to use!`);
    console.log(`   Usage: t('${key}')`);
    
  } catch (error) {
    console.error('❌ Error creating translation key:', error.message);
    process.exit(1);
  }
}

// Main function
async function main() {
  const args = parseArgs();
  
  if (args.flags.help) {
    return;
  }
  
  if (args.flags.interactive) {
    const interactiveArgs = await interactiveMode();
    await createTranslationKey(interactiveArgs.key, interactiveArgs.content, interactiveArgs.flags);
  } else {
    await createTranslationKey(args.key, args.content, args.flags);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
