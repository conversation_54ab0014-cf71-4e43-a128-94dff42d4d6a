#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate TypeScript types from translation keys in the database
 * This keeps the types in sync with the actual translation keys
 * 
 * Usage: node scripts/generate-translation-types.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration
const SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc3OTYyNDYsImV4cCI6MjA1MzM3MjI0Nn0._mrkLcgDRn-ejKwHGEC49L2C4SVvNdBcLfJbaFwpTkU';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function generateTranslationTypes() {
  try {
    console.log('🔄 Fetching translation keys from database...');
    
    // Fetch all translation keys
    const { data: keys, error } = await supabase
      .from('translation_keys')
      .select('key')
      .order('key');

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!keys || keys.length === 0) {
      throw new Error('No translation keys found in database');
    }

    console.log(`✅ Found ${keys.length} translation keys`);

    // Group keys by category
    const categories = {};
    keys.forEach(({ key }) => {
      const [category] = key.split('.');
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(key);
    });

    // Generate TypeScript content
    const typeContent = generateTypeScriptContent(keys, categories);

    // Write to file
    const outputPath = path.join(process.cwd(), 'client/src/types/translations.ts');
    fs.writeFileSync(outputPath, typeContent, 'utf8');

    console.log(`✅ Generated TypeScript types: ${outputPath}`);
    console.log(`📊 Categories: ${Object.keys(categories).join(', ')}`);
    console.log(`🔑 Total keys: ${keys.length}`);

  } catch (error) {
    console.error('❌ Error generating translation types:', error.message);
    process.exit(1);
  }
}

function generateTypeScriptContent(keys, categories) {
  const timestamp = new Date().toISOString().split('T')[0];
  
  return `/**
 * TypeScript types for translation keys
 * Auto-generated from database translation keys
 * 
 * This file provides compile-time validation for translation key usage
 * and prevents typos in t() function calls.
 * 
 * Last updated: ${timestamp}
 * Total keys: ${keys.length}
 * 
 * To regenerate: npm run generate-types
 */

/**
 * All available translation keys in the system
 * Use these with the t() function: t('hero.title_main')
 */
export type TranslationKey = ${keys.map(({ key }) => `\n  | '${key}'`).join('')};

/**
 * Translation key categories for better organization
 */
export type TranslationCategory = ${Object.keys(categories).map(cat => `\n  | '${cat}'`).join('')};

/**
 * Helper type to get keys by category
 */
export type KeysByCategory<T extends TranslationCategory> = ${Object.keys(categories).map(cat => 
  `\n  T extends '${cat}' ? Extract<TranslationKey, \`${cat}.\${string}\`> :`
).join('')}
  never;

/**
 * Content type classification
 */
export type ContentType = 'shared' | 'us-only' | 'belgium-only';

/**
 * Supported languages
 */
export type Language = 'en' | 'fr' | 'nl';

/**
 * Countries where content is shown
 */
export type Country = 'US' | 'BE';

/**
 * Translation function type with strict key validation
 */
export type TranslationFunction = (key: TranslationKey) => string;

/**
 * Translation hook return type
 */
export interface UseTranslationReturn {
  t: TranslationFunction;
  language: Language;
  country: Country;
  isLoading: boolean;
}

/**
 * Utility type to validate translation key at compile time
 * Usage: const key: ValidTranslationKey<'hero.title'> = 'hero.title';
 */
export type ValidTranslationKey<T extends string> = T extends TranslationKey ? T : never;

/**
 * Category-specific key types for better organization
 */
${Object.entries(categories).map(([category, categoryKeys]) => 
  `export type ${category.charAt(0).toUpperCase() + category.slice(1)}Keys = ${categoryKeys.map(key => `\n  | '${key}'`).join('')};`
).join('\n\n')}
`;
}

// Run the script
generateTranslationTypes();
